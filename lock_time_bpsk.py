import numpy as np
import matplotlib.pyplot as plt
from matplotlib_config import configure

CHINESE_FONT_AVAILABLE = configure()

def rrc(t, rolloff_factor):
    h = np.zeros(len(t))
    for i in range(len(t)):
        if t[i] == 0:
            h[i] = 1 - rolloff_factor + (4 * rolloff_factor / np.pi)
        elif abs(t[i]) == 1 / (4 * rolloff_factor):
            h[i] = (rolloff_factor / np.sqrt(2)) * ((1 + 2 / np.pi) * np.sin(np.pi / (4 * rolloff_factor)) + (1 - 2 / np.pi) * np.cos(np.pi / (4 * rolloff_factor)))
        else:
            h[i] = (np.sin(np.pi * t[i] * (1 - rolloff_factor)) + 4 * rolloff_factor * t[i] * np.cos(np.pi * t[i] * (1 + rolloff_factor)))
            h[i] /= (np.pi * t[i] * (1 - (4 * rolloff_factor * t[i]) ** 2))
    return h


def generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor):
    simulation_time = num_symbols / symbol_rate
    t = np.arange(0, simulation_time, time_granularity)

    impulse_series = np.zeros(len(t))
    for i in range(num_symbols):
        impulse_series[round(i / symbol_rate / time_granularity)] = message[i]

    # RRC filter
    filter_length = round(filter_symbols / symbol_rate / time_granularity)
    if filter_length % 2 == 0:
        filter_length += 1
    t_filter = np.arange(-(filter_length - 1) / 2, (filter_length + 1) / 2) * time_granularity * symbol_rate
    rrc_response = rrc(t_filter, rolloff_factor)

    baseband_signal = np.convolve(impulse_series, rrc_response)
    t = np.arange(0, len(baseband_signal) * time_granularity, time_granularity)
    return t, baseband_signal


def sample_and_filter(signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor):
    sample_rate = symbol_rate * over_sampling_ratio
    num_samples = (num_symbols + filter_symbols - 1) * over_sampling_ratio
    sampled_signal = np.zeros(num_samples, dtype=complex)
    for i in range(num_samples):
        sampled_signal[i] = signal[round(((i / sample_rate) + (sample_offset / symbol_rate)) / time_granularity)]

    discrete_filter_length = round(filter_symbols * over_sampling_ratio)
    if discrete_filter_length % 2 == 0:
        discrete_filter_length += 1
    t_discrete_filter = np.arange(-(discrete_filter_length - 1) / 2, (discrete_filter_length + 1) / 2) / over_sampling_ratio
    discrete_rrc_response = rrc(t_discrete_filter, rolloff_factor) / over_sampling_ratio

    filtered_signal = np.convolve(sampled_signal, discrete_rrc_response)
    return filtered_signal


def detect_signal(signal, energy_threshold, consecutive_threshold):
    signal_energy = np.abs(signal) ** 2
    signal_detected = signal_energy > energy_threshold
    consecutive_count = 0
    for i in range(len(signal_detected)):
        if signal_detected[i]:
            consecutive_count += 1
            if consecutive_count >= consecutive_threshold:
                return i + 1
        else:
            consecutive_count = 0
    return -1


def sync_carrier_monotone(signal, loop_bandwidth, damping_factor, VCO_gain):
    length = len(signal)
    freq_synced_signal = np.zeros(length, dtype=complex)
    phase_error = np.zeros(length)
    freq = np.zeros(length)

    natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
    denominator = 4 + 4 * damping_factor * natural_freq + (natural_freq ** 2)
    C1 = 8 * damping_factor * natural_freq / denominator
    C2 = 4 * (natural_freq ** 2) / denominator

    phase = 0
    loop_filter_state = 0
    for i in range(length):
        freq_synced_signal[i] = signal[i] * np.exp(-1j * phase)
        phase_error[i] = np.angle(freq_synced_signal[i])
        loop_filter_state = loop_filter_state + C2 * phase_error[i]
        loop_filter_out = C1 * phase_error[i] + loop_filter_state
        phase = phase + VCO_gain * loop_filter_out
        freq[i] = VCO_gain * loop_filter_out / 2 / np.pi

    return freq_synced_signal, phase_error, freq


def sync_clock(signal, num_symbols, over_sampling_ratio, filter_symbols):
    sync_offset = filter_symbols * over_sampling_ratio
    clock_synced_signal = np.zeros(num_symbols, dtype=complex)
    for i in range(num_symbols):
        clock_synced_signal[i] = signal[i * over_sampling_ratio + sync_offset]
    return clock_synced_signal


def sync_carrier_bpsk(signal, loop_bandwidth, damping_factor, VCO_gain):
    length = len(signal)
    freq_synced_signal = np.zeros(length, dtype=complex)
    phase_error = np.zeros(length)
    freq = np.zeros(length)

    natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
    denominator = 4 + 4 * damping_factor * natural_freq + (natural_freq ** 2)
    C1 = 8 * damping_factor * natural_freq / denominator
    C2 = 4 * (natural_freq ** 2) / denominator

    phase = 0
    loop_filter_state = 0
    for i in range(length):
        freq_synced_signal[i] = signal[i] * np.exp(-1j * phase)
        # phase_error[i] = np.real(freq_synced_signal[i]) * np.imag(freq_synced_signal[i])
        phase_error[i] = np.angle(np.sign(np.real(freq_synced_signal[i])) * freq_synced_signal[i])
        loop_filter_state = loop_filter_state + C2 * phase_error[i]
        loop_filter_out = C1 * phase_error[i] + loop_filter_state
        phase = phase + VCO_gain * loop_filter_out
        freq[i] = VCO_gain * loop_filter_out / 2 / np.pi

    return freq_synced_signal, phase_error, freq


def plot_sync_freq(freq_synced_signal, phase_error, freq):
    plt.figure(figsize=(5, 5))
    plt.scatter(np.real(freq_synced_signal), np.imag(freq_synced_signal))
    plt.title("Constellation Diagram")
    plt.xlabel("In-Phase")
    plt.ylabel("Quadrature")
    plt.grid()

    plt.figure(figsize=(10, 5))
    plt.plot(phase_error)
    plt.title("Phase in Carrier Synchronization")
    plt.xlabel("Sample Index")
    plt.ylabel("Phase (radians)")
    plt.grid()

    plt.figure(figsize=(10, 5))
    plt.plot(freq)
    plt.title("Frequency in Carrier Synchronization")
    plt.xlabel("Sample Index")
    plt.ylabel("Frequency (Hz)")
    plt.grid()

    plt.show()


def lock_time_monotone():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 40 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = np.ones(num_symbols)
    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    # freq_offset = 43600 # Hz
    freq_offset_list = np.arange(0, 87200, 100) # Hz
    phase_offset = 0 # radians
    noise_std = 0.01

    lock_time_list = []
    for freq_offset in freq_offset_list:

        received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
        received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
        received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

        over_sampling_ratio = 4 # sps, samples per symbol
        sample_offset = 0 # per symbol, from 0 to 1/sps

        sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)

        energy_threshold = 0.1
        consecutive_threshold = 8
        signal_start = detect_signal(sampled_signal, energy_threshold, consecutive_threshold)
        if signal_start == -1:
            print("Signal not detected")
            continue

        detected_signal = sampled_signal[signal_start:-2*filter_symbols * over_sampling_ratio]

        loop_bandwidth = 0.1 # Loop bandwidth (Normalized)
        damping_factor = 0.707 # Damping factor
        VCO_gain = 1 # VCO gain

        freq_synced_signal, phase_error, freq = sync_carrier_monotone(detected_signal, loop_bandwidth, damping_factor, VCO_gain)
        freq = freq * symbol_rate * over_sampling_ratio
        # if freq_offset > 35000:
        #     plot_sync_freq(freq_synced_signal, phase_error, freq)
        freq_requirement = 5000 # Hz
        delta_freq = np.abs(freq - freq_offset)
        freq_satisfied = np.nonzero(delta_freq > freq_requirement)[0]
        if len(freq_satisfied) == 0:
            lock_sample = 0
        else:
            lock_sample = max(freq_satisfied)
        lock_time = lock_sample / symbol_rate / over_sampling_ratio
        lock_time_list.append(lock_time)

    plt.figure(figsize=(10, 5))
    plt.plot(freq_offset_list, np.array(lock_time_list) * 1e6, marker='.')
    plt.title("Carrier Synchronization Lock Time")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Lock Time (us)")
    plt.grid()

    plt.figure(figsize=(10, 5))
    plt.plot(freq_offset_list, np.array(lock_time_list) * symbol_rate, marker='.')
    plt.title("Carrier Synchronization Lock Time")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Lock Time (symbols)")
    plt.grid()
    plt.show()


def lock_time_bpsk():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 100 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = [1, -1] * int(num_symbols / 2)
    message = np.array(message)
    # message = np.random.randint(0, 2, num_symbols)
    # message = 2 * message - 1 # Convert to bipolar

    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    # freq_offset = 1e3 # Hz
    freq_offset_list = np.arange(0, 10001, 100) # Hz
    # phase_offset = 0 # radians
    noise_std = 0.02

    lock_time_list = []

    for freq_offset in freq_offset_list:

        phase_offset = freq_offset / symbol_rate * 2 * np.pi

        received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
        received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
        received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

        over_sampling_ratio = 4 # sps, samples per symbol
        sample_offset = 0 # per symbol, from 0 to 1/sps

        sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)

        clock_synced_signal = sync_clock(sampled_signal, num_symbols, over_sampling_ratio, filter_symbols)
        clock_synced_signal = clock_synced_signal[:-filter_symbols]

        loop_bandwidth = 0.063 # Loop bandwidth (Normalized)
        damping_factor = 0.707 # Damping factor
        VCO_gain = 1 # VCO gain

        freq_synced_signal, phase_error, freq = sync_carrier_bpsk(clock_synced_signal, loop_bandwidth, damping_factor, VCO_gain)
        freq = freq * symbol_rate
        # if freq_offset > 4500:
        #     plot_sync_freq(freq_synced_signal, phase_error, freq)
        freq_requirement = 1000 # Hz
        delta_freq = np.abs(freq - freq_offset)
        freq_satisfied = np.nonzero(delta_freq > freq_requirement)[0]
        if len(freq_satisfied) == 0:
            lock_symbol = 0
        else:
            lock_symbol = max(freq_satisfied)
        lock_time = lock_symbol / symbol_rate
        lock_time_list.append(lock_time)

    plt.figure(figsize=(10, 5))
    plt.plot(freq_offset_list, np.array(lock_time_list) * 1e6, marker='.')
    plt.title("Carrier Synchronization Lock Time")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Lock Time (us)")
    plt.grid()

    plt.figure(figsize=(10, 5))
    plt.plot(freq_offset_list, np.array(lock_time_list) * symbol_rate, marker='.')
    plt.title("Carrier Synchronization Lock Time")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Lock Time (symbols)")
    plt.grid()
    plt.show()

    natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
    max_freq_offset = damping_factor * natural_freq / 2 * symbol_rate
    print(f"Max frequency offset: {max_freq_offset / 1e3:.3f} kHz")
    max_lock_time = 2 * np.pi / natural_freq / symbol_rate
    print(f"Max lock time: {max_lock_time * 1e6:.3f} us")
    max_lock_symbol = round(max_lock_time * symbol_rate)
    print(f"Max lock symbol: {max_lock_symbol} symbols")

    plt.show()


if __name__ == "__main__":
    # lock_time_monotone()
    lock_time_bpsk()
