import numpy as np
from scipy import signal
import matplotlib.pyplot as plt
from matplotlib_config import configure

CHINESE_FONT_AVAILABLE = configure()

def generate_baseband(modulation_type, duration, fs, symbol_rate=None, num_symbols=None):
    """
    生成基带信号
    modulation_type: "single_tone", "bpsk", "qpsk"
    duration: 信号持续时间（秒）
    fs: 采样率
    symbol_rate: 符号速率（用于BPSK和QPSK）
    num_symbols: 符号数量（用于BPSK和QPSK）
    """
    t = np.arange(0, duration, 1/fs)

    if modulation_type == "single_tone":
        # 生成单频信号
        signal_data = np.ones_like(t)

    elif modulation_type == "bpsk":
        # 生成BPSK信号
        if num_symbols is None:
            num_symbols = int(duration * symbol_rate)
        symbols = np.random.choice([-1, 1], num_symbols)
        # 上采样
        symbols_upsampled = np.repeat(symbols, int(fs/symbol_rate))
        # 确保长度匹配
        if len(symbols_upsampled) > len(t):
            symbols_upsampled = symbols_upsampled[:len(t)]
        elif len(symbols_upsampled) < len(t):
            symbols_upsampled = np.pad(symbols_upsampled, (0, len(t) - len(symbols_upsampled)))
        signal_data = symbols_upsampled

    elif modulation_type == "qpsk":
        # 生成QPSK信号
        if num_symbols is None:
            num_symbols = int(duration * symbol_rate)
        # 生成QPSK符号
        symbols = (np.random.choice([-1, 1], num_symbols) +
                  1j * np.random.choice([-1, 1], num_symbols)) / np.sqrt(2)
        # 上采样
        symbols_upsampled = np.repeat(symbols, int(fs/symbol_rate))
        # 确保长度匹配
        if len(symbols_upsampled) > len(t):
            symbols_upsampled = symbols_upsampled[:len(t)]
        elif len(symbols_upsampled) < len(t):
            symbols_upsampled = np.pad(symbols_upsampled, (0, len(t) - len(symbols_upsampled)))
        signal_data = symbols_upsampled

    return signal_data, t

def design_rrc_filter(symbol_rate, fs, num_symbols, beta):
    """
    设计根升余弦滤波器
    symbol_rate: 符号速率
    fs: 采样率
    num_symbols: 滤波器长度（符号数）
    beta: 滚降因子
    """
    # 计算滤波器长度（采样点数）
    filter_length = int(num_symbols * fs / symbol_rate)

    # 确保滤波器长度为奇数
    if filter_length % 2 == 0:
        filter_length += 1

    # 计算时间序列
    t = np.arange(-(filter_length-1)/2, (filter_length-1)/2 + 1) / fs

    # 计算RRC滤波器系数
    h = np.zeros_like(t)
    for i in range(len(t)):
        if t[i] == 0:
            h[i] = 1 - beta + 4 * beta / np.pi
        elif abs(t[i]) == 1 / (4 * beta * symbol_rate):
            h[i] = beta / np.sqrt(2) * ((1 + 2/np.pi) * np.sin(np.pi/(4*beta)) +
                                       (1 - 2/np.pi) * np.cos(np.pi/(4*beta)))
        else:
            h[i] = (np.sin(np.pi * symbol_rate * t[i] * (1 - beta)) +
                    4 * beta * symbol_rate * t[i] * np.cos(np.pi * symbol_rate * t[i] * (1 + beta))) / \
                   (np.pi * symbol_rate * t[i] * (1 - (4 * beta * symbol_rate * t[i])**2))

    return h

def apply_rrc_filter(signal_data, h):
    """
    应用RRC滤波器
    signal_data: 输入信号
    h: 滤波器系数
    """
    # 使用卷积进行滤波
    filtered_signal = signal.convolve(signal_data, h, mode="same")
    return filtered_signal

def channel_model(signal_data, t, freq_offset=0, phase_offset=0, attenuation=0, noise_std=0):
    """
    模拟信道
    signal_data: 输入信号
    t: 时间序列
    freq_offset: 频率偏移
    phase_offset: 相位偏移
    attenuation: 衰减（dB）
    noise_std: 噪声标准差
    """
    freq_shifted_signal = signal_data * np.exp(1j * 2 * np.pi * freq_offset * t)
    phase_shifted_signal = freq_shifted_signal * np.exp(1j * phase_offset)
    attenuated_signal = phase_shifted_signal * 10**(-attenuation/20)
    noisy_signal = attenuated_signal + np.random.normal(0, noise_std, len(t))
    return noisy_signal

def carrier_sync(signal_data, t, fs, loop_bw=0.063, modulation_type="bpsk"):
    """
    使用Costas环实现载波同步
    t: 时间序列
    signal_data: 输入信号
    fs: 采样率
    loop_bw: 环路带宽
    modulation_type: 调制类型，可选 "bpsk" 或 "qpsk"
    """
    # 初始化相位估计和频率估计
    # phase_est = np.zeros(len(signal_data))
    freq_est = np.zeros(len(signal_data))

    # 计算Costas环参数（基于理论公式）
    # 阻尼系数 ζ = 1/√2 (临界阻尼)
    zeta = 1.0 / np.sqrt(2.0)
    # 自然频率 ωn = 2πBL/2ζ
    wn = loop_bw / zeta

    # 计算环路滤波器参数
    # 比例增益 Kp = 2ζωn
    Kp = 2.0 * zeta * wn
    # 积分增益 Ki = ωn²
    Ki = wn * wn

    # 初始化积分器状态
    integrator = 0.0

    # 主循环
    for i in range(1, len(signal_data)):
        # 生成本地载波
        local_i = np.sin(2 * np.pi * freq_est[i-1] * t[i])
        local_q = np.cos(2 * np.pi * freq_est[i-1] * t[i])

        baseband_i = signal_data[i] * local_i
        baseband_q = signal_data[i] * local_q

        # low pass filter
        baseband_i = signal.filtfilt([1], [1, 0.9], baseband_i)
        baseband_q = signal.filtfilt([1], [1, 0.9], baseband_q)

        # 下变频（混频）
        baseband = signal_data[i] * local_carrier

        # 相位检测器（根据调制类型选择）
        if modulation_type.lower() == "bpsk":
            # BPSK Costas环相位检测器: I*Q
            phase_error = baseband.real * baseband.imag
        elif modulation_type.lower() == "qpsk":
            # QPSK Costas环相位检测器: sign(I)*Q - sign(Q)*I
            phase_error = np.sign(baseband.real) * baseband.imag - np.sign(baseband.imag) * baseband.real
        else:  # 默认为单音调或未知调制
            # 通用二阶PLL相位检测器
            phase_error = np.imag(baseband)

        # 环路滤波器（PI控制器）
        prop_term = Kp * phase_error
        integrator += Ki * phase_error
        loop_filter_out = prop_term + integrator

        # 频率估计更新
        freq_est[i] = loop_filter_out

        # 相位估计更新
        phase_est[i] = phase_est[i-1] + 2 * np.pi * freq_est[i] / fs

        # 相位归一化到 [-π, π]
        phase_est[i] = np.mod(phase_est[i] + np.pi, 2 * np.pi) - np.pi

    return phase_est, freq_est

def plot_results(t, signal_data, transmitted_signal, received_signal, baseband_signal, phase_est, freq_est, modulation_type, fs):
    """
    绘制结果
    t: 时间序列
    signal_data: 原始基带信号
    transmitted_signal: 发送信号
    received_signal: 接收信号
    baseband_signal: 匹配滤波后的基带信号
    phase_est: 相位估计
    freq_est: 频率估计
    modulation_type: 调制类型
    fs: 采样率
    """
    plt.figure(figsize=(15, 15))
    trunc_len = 100

    # 基带信号时域波形
    plt.subplot(331)
    plt.plot(t[:trunc_len], signal_data.real[:trunc_len], label="实部")
    plt.plot(t[:trunc_len], signal_data.imag[:trunc_len], label="虚部")
    plt.title("基带信号时域波形")
    plt.xlabel("时间 (s)")
    plt.ylabel("幅度")
    plt.legend()
    plt.grid(True)

    # 基带信号频谱
    plt.subplot(332)
    plt.magnitude_spectrum(signal_data, Fs=fs, scale="dB")
    plt.title("基带信号频谱")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("幅度 (dB)")
    plt.grid(True)

    # 基带信号星座图
    plt.subplot(333)
    plt.scatter(signal_data.real, signal_data.imag, alpha=0.5)
    plt.title("基带信号星座图")
    plt.grid(True)

    # 发送信号时域波形
    plt.subplot(334)
    plt.plot(t[:trunc_len], transmitted_signal.real[:trunc_len], label="实部")
    plt.plot(t[:trunc_len], transmitted_signal.imag[:trunc_len], label="虚部")
    plt.title("发送信号时域波形")
    plt.xlabel("时间 (s)")
    plt.ylabel("幅度")
    plt.legend()
    plt.grid(True)

    # 发送信号频谱
    plt.subplot(335)
    plt.magnitude_spectrum(transmitted_signal, Fs=fs, scale="dB")
    plt.title("发送信号频谱")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("幅度 (dB)")
    plt.grid(True)

    # 发送信号星座图
    plt.subplot(336)
    plt.scatter(transmitted_signal.real, transmitted_signal.imag, alpha=0.5)
    plt.title("发送信号星座图")
    plt.grid(True)

    # 接收信号时域波形
    plt.subplot(337)
    plt.plot(t[:trunc_len], received_signal.real[:trunc_len], label="实部")
    plt.plot(t[:trunc_len], received_signal.imag[:trunc_len], label="虚部")
    plt.title("接收信号时域波形")
    plt.xlabel("时间 (s)")
    plt.ylabel("幅度")
    plt.legend()
    plt.grid(True)

    # 接收信号频谱
    plt.subplot(338)
    plt.magnitude_spectrum(received_signal, Fs=fs, scale="dB")
    plt.title("接收信号频谱")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("幅度 (dB)")
    plt.grid(True)

    # 接收信号星座图
    plt.subplot(339)
    plt.scatter(received_signal.real, received_signal.imag, alpha=0.5)
    plt.title("接收信号星座图")
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(f"transmission_{modulation_type}.png", dpi=300, bbox_inches="tight")
    plt.close()

    # 画出同步后的信号
    plt.figure(figsize=(15, 15))

    # 画出匹配滤波后的基带信号
    plt.subplot(331)
    plt.plot(t[:trunc_len], baseband_signal.real[:trunc_len], label="实部")
    plt.plot(t[:trunc_len], baseband_signal.imag[:trunc_len], label="虚部")
    plt.title("匹配滤波后的基带信号")
    plt.xlabel("时间 (s)")
    plt.ylabel("幅度")
    plt.legend()
    plt.grid(True)

    # 画出匹配滤波后的基带信号的频谱
    plt.subplot(332)
    plt.magnitude_spectrum(baseband_signal, Fs=fs, scale="dB")
    plt.title("匹配滤波后的基带信号频谱")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("幅度 (dB)")
    plt.grid(True)

    # 画出匹配滤波后的基带信号的星座图
    plt.subplot(333)
    plt.scatter(baseband_signal.real, baseband_signal.imag, alpha=0.5)
    plt.title("匹配滤波后的基带信号星座图")
    plt.grid(True)

    sync_signal = baseband_signal * np.exp(-1j * (2 * np.pi * freq_est * t + phase_est))
    # 画出同步后的信号
    plt.subplot(334)
    plt.plot(t[:trunc_len], sync_signal.real[:trunc_len], label="实部")
    plt.plot(t[:trunc_len], sync_signal.imag[:trunc_len], label="虚部")
    plt.title("同步后的信号")
    plt.xlabel("时间 (s)")
    plt.ylabel("幅度")
    plt.legend()

    # 画出同步后的信号的频谱
    plt.subplot(335)
    plt.magnitude_spectrum(sync_signal, Fs=fs, scale="dB")
    plt.title("同步后的信号频谱")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("幅度 (dB)")
    plt.grid(True)

    # 画出同步后的信号的星座图
    plt.subplot(336)
    plt.scatter(sync_signal.real, sync_signal.imag, alpha=0.5)

    # 画出同步过程中的频率估计
    plt.subplot(337)
    plt.plot(t, freq_est)
    plt.title("同步过程中的频率估计")
    plt.xlabel("时间 (s)")
    plt.ylabel("频率 (Hz)")
    plt.grid(True)

    # 画出同步过程中的相位估计
    plt.subplot(338)
    plt.plot(t, phase_est)
    plt.title("同步过程中的相位估计")
    plt.xlabel("时间 (s)")
    plt.ylabel("相位 (rad)")
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(f"sync_{modulation_type}.png", dpi=300, bbox_inches="tight")
    plt.close()


def main():
    # 参数设置
    bw = 2e6  # 带宽2MHz
    fs = 4e6  # 采样率4MHz
    freq_offset = 1000  # 频率偏移1000Hz
    duration = 1e-3  # 信号持续时间1ms
    beta = 0.35  # 滚降因子
    symbol_rate = bw / 2  # 符号速率，需要小于 bw / (1 + beta)

    # 设计RRC滤波器
    h = design_rrc_filter(symbol_rate, fs, 8, beta)

    # # 画出RRC滤波器
    # plt.figure(figsize=(10, 5))
    # plt.plot(h)
    # plt.title("RRC滤波器")
    # plt.xlabel("时间")
    # plt.ylabel("幅度")
    # plt.grid(True)
    # plt.show()

    # # 画出RRC滤波器的频谱
    # plt.figure(figsize=(10, 5))
    # plt.magnitude_spectrum(h, Fs=fs, scale="dB")
    # plt.title("RRC滤波器频谱")
    # plt.xlabel("频率 (Hz)")
    # plt.ylabel("幅度 (dB)")
    # plt.grid(True)
    # plt.show()
    # return

    # 调制类型列表
    modulation_types = ["single_tone", "bpsk", "qpsk"]

    for mod_type in modulation_types:
        print(f"\n处理 {mod_type} 信号...")

        # 在基带生成信号
        signal_data, t = generate_baseband(mod_type, duration, fs, symbol_rate)

        # 应用RRC滤波器
        transmitted_signal = apply_rrc_filter(signal_data, h)

        # 模拟信道
        received_signal = channel_model(transmitted_signal, t, freq_offset=freq_offset)

        # 应用RRC滤波器
        baseband_signal = apply_rrc_filter(received_signal, h)

        # 载波同步
        phase_est, freq_est = carrier_sync(baseband_signal, fs, modulation_type=mod_type)

        # 绘制结果
        plot_results(t, signal_data, transmitted_signal, received_signal, baseband_signal, phase_est, freq_est, mod_type, fs)

        # 计算同步时间（当频率估计误差小于1Hz时认为同步完成）
        sync_time = 0
        for i in range(len(freq_est)):
            if abs(freq_est[i] - freq_offset) < 1:
                sync_time = t[i]
                break

        print(f"{mod_type} 同步时间: {sync_time*1000:.2f} ms")

if __name__ == "__main__":
    main()
