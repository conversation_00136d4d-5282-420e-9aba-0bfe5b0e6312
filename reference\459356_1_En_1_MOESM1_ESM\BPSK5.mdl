Model {
  Name			  "BPSK5"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.87"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model BPSK5\n----------------------\n\nThis model represents a digital Costas loop based on rotation"
  " of the (I, Q) vector.\nThe blocks Carrier, Random, Relay and Modulation on the left side of the model\ncreate a BPS"
  "K signal. Its carrier frequency is fC. The local quadrature oscillator\nbuilt from the blocks sin and cos create a \""
  "wrong\" local frequency f0 that is purposedly\noffset from the carrier frequency. MixerI and MixerQ are used to crea"
  "te the demodulated\nsignal I, Q. These 2 signals are \"rotated\" by a frequency that equals the frequency\noffset fC"
  " - f0. Lowpass filters F1(z) and F2(z) remove the higher frequency components.\n\nNow a vector rotator is used to ch"
  "ange the phase of vector (I, Q) such that the \nrotated vector (I', Q') has either phase 0 or 180 degrees. Doing so "
  "sets the Q' \ncomponent to nearly 0, and I' is the required demodulated BPSK signal. \n\nThe rotator is shown on bot"
  "tom of the block diagram. An up/down counter is used\nto rotate the I,Q vector clockwise or counterclockwise. The an"
  "alysis shows that\nthe vector must be rotated positively when the EXOR function of the quantized\nI', Q' vector is T"
  "RUE. This is shown in the center of the diagram. Relay1 and Relay2\nare used to quantize the I',Q' vector. \nThe out"
  "put signal of the EXOR controls the counting direction (Select Up/Down).\nThe counter is built from an accumulator c"
  "onsisting of an adder block entitled\nCounterACC. The counter must wrap to zero when it counts forward and its conte"
  "nt\nwould become larger than 15. In analogy, the counter must wrap to 15 when it \ncounts down and its content would"
  " get less than 0. The counter output is labeled\nC_out. The counter output is multiplied by the phase increment pi/8"
  " (22.5 degrees).\nThe blocks labeled CosGen and SinGen compute sine and cosine of the\naccumated phase, i.e. from C_"
  "out * pi/8. \n\nNow the vector I,Q is rotated by computing\n\n          I' = I * cos(phi_acc) - Q * sin(phi_acc)\n  "
  "        Q' = I * sin(phi_acc) + Q * cos(phi_acc)\n\nwhere phi_acc is the accumulated phase. \n\nNote that the I'_del"
  " signal is a quantized version of the output signal I'. I'_del is\nconsequently the desired demodulated BPSK signal."
  "\n\nNote1: the delay blocks DelayI and DelayQ had to be inserted to avoid an\nalgebraic loop. \n\nNote2: The vector "
  "rotator is clocked with a sampling frequency fclock given by \n\n     fclock = OS * fR\n\nwith OS = oversampling fac"
  "tor, fR = symbol rate (bits/s)\n\nNote3: When the Costas loop has settled, the content of the Up/Down counter \ntogg"
  "les between the adjacent values. e.g. 5 and 6, respectively. Doing so the rotated\nI',Q' vector toggles swings aroun"
  "d the horizontal axis, with I' nearly 1 or -1, and with\nQ' nearly 0. \n\nNote 4: When the operator specifies a freq"
  "uency offset df equal to the\ncarrier frequency fC, this would set the local oscillator frequency f0 to 0,\nand the "
  "sample time of the blocks cos and sin would be set to inf.\nThis must be avoided, because when this happens, Simulin"
  "k is unable\nto overwrite the sampling time with a new value, when the operator \nspecifies another frequency error "
  "at the next simulation. Whenever the\noperator specifies df = 0, this is replaced by df = 10'000.\nPull-in range of "
  "this Costas loop\n------------------------------------\nThe pull-in range is given by the maximum rotation speed of "
  "the phasor I, Q. \nSymbol frequency fs is 100 kHz, and the sampling frequency of the rotator \n(cf. phi_out) is 1.6 "
  "MHz, hence there are 16 samples within one symbol interval Ts.\nThe phasor I, Q can be rotated in steps of 2 * pi/16"
  ", hence within one symbol\ninterval it can be rotated byu an angle of 2 * pi, which corresponds to a \nfrequency dev"
  "iation df of 100 kHz. The theoretical pull-in range is therefore\n+ - 100 kHz.\nSimulations performed with the model"
  " show that the pull-in range of \nfP = 100 kHz can be realized. "
  SavedCharacterEncoding  "windows-1252"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  InitFcn		  "StartFunctionBPSK5"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Mon Jan 30 12:58:03 2017"
  RTWModifiedTimeStamp	  407671455
  ModelVersionFormat	  "1.%<AutoIncrement:87>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "auto"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "ode45"
	  SolverName		  "ode45"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  off
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "warning"
	  UnconnectedOutputMsg	  "warning"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Optimization"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      CombinatorialLogic
      TruthTable	      "[0 0;0 1;0 1;1 0;0 1;1 0;1 0;1 1]"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      InitialStates	      "0"
      SampleTime	      "1"
      a0EqualsOne	      off
      NumCoefMin	      "[]"
      NumCoefMax	      "[]"
      DenCoefMin	      "[]"
      DenCoefMax	      "[]"
      OutMin		      "[]"
      OutMax		      "[]"
      StateDataTypeStr	      "Inherit: Same as input"
      NumCoefDataTypeStr      "Inherit: Inherit via internal rule"
      DenCoefDataTypeStr      "Inherit: Inherit via internal rule"
      NumProductDataTypeStr   "Inherit: Inherit via internal rule"
      DenProductDataTypeStr   "Inherit: Inherit via internal rule"
      NumAccumDataTypeStr     "Inherit: Inherit via internal rule"
      DenAccumDataTypeStr     "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParamMin		      "[]"
      ParamMax		      "[]"
      ParamDataTypeStr	      "Inherit: Same as input"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Mux
      Inputs		      "4"
      DisplayOption	      "none"
      UseBusObject	      off
      BusObject		      "BusObject"
      NonVirtualBus	      off
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Switch
      Criteria		      "u2 >= Threshold"
      Threshold		      "0"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      ZeroCross		      on
      SampleTime	      "-1"
      AllowDiffInputSizes     off
    }
    Block {
      BlockType		      Trigonometry
      Operator		      "sin"
      ApproximationMethod     "None"
      NumberOfIterations      "11"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      UnitDelay
      X0		      "0"
      InputProcessing	      "Inherited"
      SampleTime	      "1"
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
  }
  System {
    Name		    "BPSK5"
    Location		    [183, 182, 1044, 877]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "42"
    Block {
      BlockType		      Constant
      Name		      "0"
      SID		      "1"
      Position		      [270, 475, 300, 505]
      Value		      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "15"
      SID		      "2"
      Position		      [360, 590, 390, 620]
      Value		      "15"
    }
    Block {
      BlockType		      Sum
      Name		      "AddI"
      SID		      "3"
      Ports		      [2, 1]
      Position		      [730, 157, 760, 188]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"I'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "AddQ"
      SID		      "4"
      Ports		      [2, 1]
      Position		      [735, 307, 765, 338]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Q'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "Carrier"
      SID		      "5"
      Ports		      [0, 1]
      Position		      [15, 70, 45, 100]
      Frequency		      "2513274.1229"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "CosGen"
      SID		      "6"
      Ports		      [1, 1]
      Position		      [550, 545, 580, 575]
      Operator		      "cos"
      Port {
	PortNumber		1
	Name			"cos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "CounterCLK"
      SID		      "7"
      Position		      [95, 535, 125, 565]
      SampleTime	      "6.25e-007"
    }
    Block {
      BlockType		      UnitDelay
      Name		      "Delay"
      SID		      "8"
      Position		      [315, 638, 350, 672]
      BlockMirror	      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UnitDelay
      Name		      "DelayI"
      SID		      "9"
      Position		      [115, 18, 150, 52]
      BlockMirror	      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UnitDelay
      Name		      "DelayQ"
      SID		      "10"
      Position		      [105, 443, 140, 477]
      BlockMirror	      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      CombinatorialLogic
      Name		      "EXOR"
      SID		      "11"
      Position		      [150, 335, 180, 365]
      TruthTable	      "[0;1;1;0]"
      Port {
	PortNumber		1
	Name			"I'_del xor Q'_del"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F1(z)"
      SID		      "12"
      Ports		      [1, 1]
      Position		      [435, 122, 495, 158]
      Numerator		      "[1  1]"
      Denominator	      "[3.4142     -1.4142]"
      SampleTime	      "6.25e-007"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F2(z)"
      SID		      "13"
      Ports		      [1, 1]
      Position		      [435, 327, 495, 363]
      Numerator		      "[1  1]"
      Denominator	      "[3.4142     -1.4142]"
      SampleTime	      "6.25e-007"
      Port {
	PortNumber		1
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Gain"
      SID		      "14"
      Position		      [610, 620, 640, 650]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "I'Q'"
      SID		      "15"
      Ports		      [2]
      Position		      [825, 216, 855, 249]
      Floating		      off
      Location		      [132, 566, 592, 805]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "50000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "IQ"
      SID		      "16"
      Ports		      [2]
      Position		      [465, 231, 495, 264]
      BlockMirror	      on
      Floating		      off
      Location		      [129, 391, 589, 630]
      Open		      on
      NumInputPorts	      "2"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData3"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "IbQb"
      SID		      "17"
      Ports		      [4]
      Position		      [225, 282, 255, 343]
      Floating		      off
      Location		      [29, 318, 354, 756]
      Open		      off
      NumInputPorts	      "4"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
	axes4			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1~-0.1~0"
      YMax		      "1.1~1.1~1.1~15"
      SaveName		      "ScopeData6"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Gain
      Name		      "Inverter"
      SID		      "18"
      Position		      [165, 575, 195, 605]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MixerI"
      SID		      "19"
      Ports		      [2, 1]
      Position		      [365, 122, 395, 153]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MixerQ"
      SID		      "20"
      Ports		      [2, 1]
      Position		      [370, 327, 400, 358]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Modulation"
      SID		      "21"
      Ports		      [2, 1]
      Position		      [165, 112, 195, 143]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"c(t)m(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "MulI1"
      SID		      "22"
      Ports		      [2, 1]
      Position		      [665, 132, 695, 163]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI2"
      SID		      "23"
      Ports		      [2, 1]
      Position		      [665, 192, 695, 223]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ1"
      SID		      "24"
      Ports		      [2, 1]
      Position		      [665, 262, 695, 293]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ2"
      SID		      "25"
      Ports		      [2, 1]
      Position		      [665, 337, 695, 368]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Mux
      Name		      "Mux"
      SID		      "26"
      Ports		      [2, 1]
      Position		      [125, 331, 130, 369]
      ShowName		      off
      Inputs		      "2"
      DisplayOption	      "bar"
    }
    Block {
      BlockType		      Scope
      Name		      "RF Signal"
      SID		      "27"
      Ports		      [2]
      Position		      [270, 246, 300, 279]
      Floating		      off
      Location		      [131, 92, 587, 331]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "Random"
      SID		      "28"
      Position		      [15, 159, 45, 191]
      SampleTime	      "1e-005"
    }
    Block {
      BlockType		      Relay
      Name		      "Relay"
      SID		      "29"
      Position		      [80, 160, 110, 190]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "Relay1"
      SID		      "30"
      Position		      [65, 310, 95, 340]
      Port {
	PortNumber		1
	Name			"I'_del"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "Relay2"
      SID		      "31"
      Position		      [65, 375, 95, 405]
      Port {
	PortNumber		1
	Name			"Q'_del"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Select\nUp/Down"
      SID		      "32"
      Position		      [225, 545, 255, 575]
      Threshold		      "0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Counter\nInput"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "SinGen"
      SID		      "33"
      Ports		      [1, 1]
      Position		      [550, 620, 580, 650]
      Port {
	PortNumber		1
	Name			"sin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "34"
      Ports		      [2, 1]
      Position		      [285, 550, 305, 570]
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ACC\nout"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "TestI"
      SID		      "35"
      Ports		      [2]
      Position		      [365, 46, 395, 79]
      Floating		      off
      Location		      [19, 68, 343, 308]
      Open		      off
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.1~-2.2"
      YMax		      "1.1~2.2"
      SaveName		      "ScopeData2"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "TestQ"
      SID		      "36"
      Ports		      [2]
      Position		      [375, 406, 405, 439]
      Floating		      off
      Location		      [18, 377, 342, 618]
      Open		      off
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-2.2~-1.1"
      YMax		      "2.2~1.1"
      SaveName		      "ScopeData4"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap<0"
      SID		      "37"
      Position		      [425, 545, 455, 575]
      Threshold		      "-0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"C_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap>15"
      SID		      "38"
      Position		      [355, 535, 385, 565]
      Threshold		      "15.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Sin
      Name		      "cos"
      SID		      "39"
      Ports		      [0, 1]
      Position		      [370, 270, 400, 300]
      BlockMirror	      on
      Amplitude		      "2"
      Frequency		      "2450442.2698"
      Phase		      "pi/2"
      SampleTime	      "8.0128e-008"
      Port {
	PortNumber		1
	Name			"LOcos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "cos/sin"
      SID		      "40"
      Ports		      [3]
      Position		      [685, 547, 720, 613]
      Floating		      off
      Location		      [121, 751, 584, 1018]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1~-1"
      YMax		      "1.1~1.1~16"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "50000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Gain
      Name		      "pi/8"
      SID		      "41"
      Position		      [490, 545, 520, 575]
      Gain		      "pi/8"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"phi_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "sin"
      SID		      "42"
      Ports		      [0, 1]
      Position		      [370, 185, 400, 215]
      BlockMirror	      on
      Amplitude		      "2"
      Frequency		      "2450442.2698"
      SampleTime	      "8.0128e-008"
      Port {
	PortNumber		1
	Name			"LOsin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0]
      SrcBlock		      "Carrier"
      SrcPort		      1
      Points		      [75, 0; 0, 35]
      DstBlock		      "Modulation"
      DstPort		      1
    }
    Line {
      Name		      "m(t)"
      Labels		      [0, 0]
      SrcBlock		      "Relay"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, -40]
	DstBlock		"Modulation"
	DstPort			2
      }
      Branch {
	Labels			[1, 0]
	Points			[100, 0; 0, 95]
	DstBlock		"RF Signal"
	DstPort			2
      }
    }
    Line {
      Name		      "c(t)m(t)"
      Labels		      [0, 0]
      SrcBlock		      "Modulation"
      SrcPort		      1
      Points		      [45, 0]
      Branch {
	Points			[0, 125]
	DstBlock		"RF Signal"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	Points			[75, 0]
	Branch {
	  DstBlock		  "MixerI"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, -75]
	  DstBlock		  "TestI"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 220]
	  Branch {
	    DstBlock		    "MixerQ"
	    DstPort		    2
	  }
	  Branch {
	    Points		    [0, 80]
	    DstBlock		    "TestQ"
	    DstPort		    2
	  }
	}
      }
    }
    Line {
      SrcBlock		      "Random"
      SrcPort		      1
      DstBlock		      "Relay"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MixerI"
      SrcPort		      1
      DstBlock		      "F1(z)"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MixerQ"
      SrcPort		      1
      DstBlock		      "F2(z)"
      DstPort		      1
    }
    Line {
      Name		      "LOsin"
      Labels		      [0, 0]
      SrcBlock		      "sin"
      SrcPort		      1
      Points		      [-25, 0; 0, -55]
      Branch {
	DstBlock		"MixerI"
	DstPort			2
      }
      Branch {
	Points			[0, -75]
	DstBlock		"TestI"
	DstPort			2
      }
    }
    Line {
      Name		      "LOcos"
      Labels		      [0, 0]
      SrcBlock		      "cos"
      SrcPort		      1
      Points		      [-20, 0; 0, 50]
      Branch {
	DstBlock		"MixerQ"
	DstPort			1
      }
      Branch {
	Points			[0, 80]
	DstBlock		"TestQ"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Delay"
      SrcPort		      1
      Points		      [-15, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "ACC\nout"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Labels			[1, 0]
	DstBlock		"Wrap>15"
	DstPort			3
      }
      Branch {
	Points			[0, -10]
	DstBlock		"Wrap>15"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "0"
      SrcPort		      1
      Points		      [15, 0; 0, 50]
      DstBlock		      "Wrap>15"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Wrap>15"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Wrap<0"
	DstPort			1
      }
      Branch {
	Points			[0, 10]
	DstBlock		"Wrap<0"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "15"
      SrcPort		      1
      Points		      [0, -35]
      DstBlock		      "Wrap<0"
      DstPort		      3
    }
    Line {
      Name		      "C_out"
      Labels		      [0, 0]
      SrcBlock		      "Wrap<0"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	Points			[0, -40; -255, 0]
	DstBlock		"IbQb"
	DstPort			4
      }
      Branch {
	DstBlock		"pi/8"
	DstPort			1
      }
      Branch {
	Points			[0, 40]
	Branch {
	  Points		  [0, 55]
	  DstBlock		  "Delay"
	  DstPort		  1
	}
	Branch {
	  DstBlock		  "cos/sin"
	  DstPort		  3
	}
      }
    }
    Line {
      SrcBlock		      "Inverter"
      SrcPort		      1
      Points		      [10, 0]
      DstBlock		      "Select\nUp/Down"
      DstPort		      3
    }
    Line {
      Name		      "Counter\nInput"
      Labels		      [0, 0]
      SrcBlock		      "Select\nUp/Down"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "CounterCLK"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, 40]
	DstBlock		"Inverter"
	DstPort			1
      }
      Branch {
	DstBlock		"Select\nUp/Down"
	DstPort			1
      }
    }
    Line {
      Name		      "sin"
      Labels		      [0, 0]
      SrcBlock		      "SinGen"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Gain"
	DstPort			1
      }
      Branch {
	Points			[0, -55]
	Branch {
	  DstBlock		  "cos/sin"
	  DstPort		  2
	}
	Branch {
	  Points		  [0, -295]
	  DstBlock		  "MulQ1"
	  DstPort		  2
	}
      }
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "F1(z)"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[20, 0]
	Branch {
	  Labels		  [1, 0]
	  DstBlock		  "MulI1"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 130]
	  DstBlock		  "MulQ1"
	  DstPort		  1
	}
      }
      Branch {
	Points			[0, 100]
	DstBlock		"IQ"
	DstPort			1
      }
    }
    Line {
      Name		      "cos"
      Labels		      [0, 0]
      SrcBlock		      "CosGen"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[0, -200]
	Branch {
	  Points		  [0, -205]
	  DstBlock		  "MulI1"
	  DstPort		  2
	}
	Branch {
	  DstBlock		  "MulQ2"
	  DstPort		  2
	}
      }
      Branch {
	Labels			[1, 0]
	DstBlock		"cos/sin"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "F2(z)"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Labels			[1, 0]
	Points			[100, 0]
	Branch {
	  Points		  [0, -145]
	  DstBlock		  "MulI2"
	  DstPort		  1
	}
	Branch {
	  DstBlock		  "MulQ2"
	  DstPort		  1
	}
      }
      Branch {
	Points			[0, -90]
	DstBlock		"IQ"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "Gain"
      SrcPort		      1
      Points		      [0, -420]
      DstBlock		      "MulI2"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulI1"
      SrcPort		      1
      Points		      [5, 0; 0, 15]
      DstBlock		      "AddI"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulI2"
      SrcPort		      1
      Points		      [5, 0; 0, -30]
      DstBlock		      "AddI"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulQ1"
      SrcPort		      1
      Points		      [10, 0; 0, 35]
      DstBlock		      "AddQ"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulQ2"
      SrcPort		      1
      Points		      [10, 0; 0, -25]
      DstBlock		      "AddQ"
      DstPort		      2
    }
    Line {
      Name		      "I'_del"
      Labels		      [0, 0]
      SrcBlock		      "Relay1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	Points			[0, 15]
	DstBlock		"Mux"
	DstPort			1
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -35]
	DstBlock		"IbQb"
	DstPort			1
      }
    }
    Line {
      Name		      "Q'_del"
      Labels		      [0, 0]
      SrcBlock		      "Relay2"
      SrcPort		      1
      Points		      [5, 0; 0, -30; 5, 0]
      Branch {
	DstBlock		"Mux"
	DstPort			2
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -55]
	DstBlock		"IbQb"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "Mux"
      SrcPort		      1
      DstBlock		      "EXOR"
      DstPort		      1
    }
    Line {
      Name		      "I'_del xor Q'_del"
      Labels		      [0, 0]
      SrcBlock		      "EXOR"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, 210]
	DstBlock		"Select\nUp/Down"
	DstPort			2
      }
      Branch {
	Points			[0, -30]
	DstBlock		"IbQb"
	DstPort			3
      }
    }
    Line {
      Name		      "I'"
      Labels		      [0, 0]
      SrcBlock		      "AddI"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[0, 50]
	DstBlock		"I'Q'"
	DstPort			1
      }
      Branch {
	Points			[0, -140]
	DstBlock		"DelayI"
	DstPort			1
      }
    }
    Line {
      Name		      "Q'"
      Labels		      [0, 0]
      SrcBlock		      "AddQ"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	Points			[0, -85]
	DstBlock		"I'Q'"
	DstPort			2
      }
      Branch {
	Points			[0, 135]
	DstBlock		"DelayQ"
	DstPort			1
      }
    }
    Line {
      Name		      "phi_out"
      Labels		      [0, 0]
      SrcBlock		      "pi/8"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"CosGen"
	DstPort			1
      }
      Branch {
	Points			[0, 75]
	DstBlock		"SinGen"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "DelayQ"
      SrcPort		      1
      Points		      [-50, 0]
      DstBlock		      "Relay2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "DelayI"
      SrcPort		      1
      Points		      [-60, 0]
      DstBlock		      "Relay1"
      DstPort		      1
    }
  }
}
