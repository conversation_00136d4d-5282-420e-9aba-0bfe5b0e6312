% Start Function of Model BPSK6
%
% Revision date: 30-JAN-2-17
%
% Enter symbol rate fR
fR = input('Enter symbol rate fR [100000]: ');
if isempty(fR)
    fR = 100000;
end

% enter frequency offset delta_f
delta_f = input('Enter initial VCO frequency offset delta_f [10000]: ');
if isempty(delta_f);
    delta_f = 10000;
end

% Enter number of symbols nCycles for simulation
nCycles = input('Enter number of symbols (bits) nCycles [20]: ');
if isempty(nCycles)
    nCycles = 20;
end

% Enter carrier frequency fC     
fC = input('Enter carrier frequency fC [400000]: ');
if isempty(fC)
    fC = 400000;
end

% compute the parameters of the model
fS = 4 * fC; % sampling frequency must be at least 4 * carrier frequency
T = 1/fS; % sampling interal
f0 = fC - delta_f;
if f0 == 0,     % avoid loc osc frequency to be set 0, because this would 
                % set the sampling time of blocks sin and cos to inf.
    f0 = 10000;
end;
omega0 = 2 * pi * f0;
tR = 1/fR; % duration of one symbol (bit)
omegaC = 2 * pi * fC;
tS = 1/(fC * 32); % sampling interval for sine generator, 32 samples per cycle
t0 = 1/(f0 * 32);
% enter parameters into the model
% set_param('BPSK6/EXOR', 'Sample time', num2str(Tclock));
set_param('BPSK6/Random', 'Sample time', num2str(tR));
set_param('BPSK6/Carrier', 'Frequency', num2str(omegaC));
set_param('BPSK6/Carrier', 'Sample time', num2str(tS));
set_param('BPSK6/Carrier1', 'Frequency', num2str(omegaC));
set_param('BPSK6/Carrier1', 'Sample time', num2str(tS));
set_param('BPSK6', 'Stop time', num2str(nCycles * tR));
set_param('BPSK6/sin', 'Frequency', num2str(omega0));
set_param('BPSK6/sin', 'Sample time', num2str(t0));
set_param('BPSK6/cos', 'Frequency', num2str(omega0));
set_param('BPSK6/cos', 'Sample time', num2str(t0));
set_param('BPSK6/CounterCLK', 'Sample time', num2str(T));
