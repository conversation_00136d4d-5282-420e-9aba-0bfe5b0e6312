import adi
import numpy as np
import transceiver
import utility


class Pluto(transceiver.BasebandTransceiver):
    def __init__(self, address, center_freq, sample_rate, buffer_size, sps=4, bandwidth=None, gain="fast_attack", rrc_filter_symbols=8, rolloff_factor=0.35):
        self.device = adi.Pluto(address)
        self.device.rx_lo = int(center_freq)
        self.device.tx_lo = int(center_freq)
        self.device.sample_rate = int(sample_rate)
        self.device.rx_buffer_size = int(buffer_size)

        if bandwidth is None:
            bandwidth = sample_rate
        self.device.rx_rf_bandwidth = int(bandwidth)
        self.device.tx_rf_bandwidth = int(bandwidth)

        if isinstance(gain, int) or isinstance(gain, float):
            self.device.gain_control_mode_chan0 = "manual"
            self.device.rx_hardwaregain_chan0 = gain
        else:
            self.device.gain_control_mode_chan0 = gain

        self.sample_gain = 2 ** 14 / 2 # input shoould be within [-2, 2]
        self.device.tx_hardwaregain_chan0 = 0 # dB

        super().__init__(
            transmit_sps=sps,
            receive_sps=sps,
            rrc_filter_symbols=rrc_filter_symbols,
            rolloff_factor=rolloff_factor
        )


    def transmit(self, data):
        symbols = super().transmit(data)
        symbols = np.concatenate((np.zeros(int(0.05 * self.device.sample_rate)), symbols))
        symbols = symbols * self.sample_gain
        self.device.tx_destroy_buffer()
        # self.device.tx_cyclic_buffer = True
        self.device.tx(symbols)


    def receive_signal(self):
        self.device.rx_destroy_buffer()
        signal = self.device.rx()
        return signal
        # return super().receive(signal)


if __name__ == "__main__":
    center_freq = 1440e6
    frequency_offset = 1e5
    bandwidth = 1e6
    rrc_filter_symbols = 8
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    over_sampling_ratio = 4
    sample_rate = symbol_rate * over_sampling_ratio
    sample_duration = 0.05 # sec
    sample_size = int(sample_rate * sample_duration)

    pluto_tx = Pluto(
        address="ip:***********",
        center_freq=center_freq,
        sample_rate=sample_rate,
        buffer_size=sample_size,
        bandwidth=bandwidth * 2,
        gain=0,
        sps=over_sampling_ratio,
        rolloff_factor=rolloff_factor
    )

    direct_gain = 0
    wireless1_gain = 20
    wireless2_gain = 40
    distance_gain = 60

    pluto_rx = Pluto(
        address="ip:***********",
        center_freq=center_freq + frequency_offset,
        sample_rate=sample_rate,
        buffer_size=sample_size,
        bandwidth=bandwidth * 2,
        gain=wireless1_gain,
        sps=over_sampling_ratio,
        rolloff_factor=rolloff_factor
    )

    data = np.random.randint(0, 2, size=1000)  # Random binary data
    pluto_tx.transmit(data)

    signal = pluto_rx.receive_signal()
    utility.plot_sampled_signal(signal)
    utility.save_signal(signal, "gain20.npy")

    data_received, receiver_states, filtered_samples, sample_freq_offsets, freq_adjusted_samples, clock_offsets, magnitudes, symbols = pluto_rx.receive(signal)
    utility.plot_sampled_signal(receiver_states, title="Receiver States")
    utility.plot_sampled_signal(filtered_samples, title="Filtered Samples")
    utility.plot_sampled_signal(np.array(sample_freq_offsets) * symbol_rate, title="Sample Frequency Offsets")
    utility.plot_sampled_signal(freq_adjusted_samples, title="Frequency Adjusted Samples")
    utility.plot_sampled_signal(clock_offsets, title="Clock Offsets")
    utility.plot_sampled_signal(magnitudes, title="AGC Magnitude")
    utility.plot_sampled_signal(symbols, title="Symbols")
    utility.plot_constellation(symbols, title="Symbols Constellation")

    if len(data) != len(data_received):
        print("Warning: Length of received message does not match transmitted data.")
    error_count = np.sum(np.abs(data - data_received[:len(data)]))
    print(f"Number of error bits: {error_count}")
