function varargout = cfo_est(varargin)
% Author: <PERSON> <PERSON> <PERSON><PERSON>@outlook.com
% 10/17/2022
% Carrier Frequency Offset Estimation.
% cfo = cfo_est(y,Nfft,Ng,'cp');
% cfo = cfo_est(y,Nfft,Nps,'pilot');
% cfo = cfo_est(y,Nfft,<PERSON>,'moose'); where, y has the size of N*2.
% cfo = cfo_est(y,xp,Nfft,Ng,idx,'classen');
% Inputs:
%   y      : received symbols in time domain or frequency domain.
%   xp     : transmitter pilot symbol in frequency domain.
%   Nfft   : FFT points.
%   Ncp    : CP length.
%   Nps    : pilot spacing, must be larger than 1.
%   idx    : index of pilot location in slots, must be a vector.
%   method : 'cp','pilot', based on time domain;
%            'moose','classen', based on frequency domain.
% Outputs:
%   cfo_est  : CFO estimation result.

global ShowFigure;

narginchk(1,6);
nargoutchk(0,2);

% parse input arguments.
lenargin = length(varargin);
if lenargin>=1
    y = varargin{1};
    method = 'cp';
    if lenargin >= 2
        method = varargin{lenargin};
    end
end

% estimate cfo.
if strcmpi(method,'cp')
    Nfft = varargin{2};
    Ncp = varargin{3};
    cfo_est = angle(y(1:Ncp,1)'*y(Nfft+1:Nfft+Ncp,1))/(2*pi);
    cfo_est = mean(cfo_est);
elseif strcmpi(method,'pilot')
    Nfft = varargin{2};
    Nps = varargin{3};
    cfo_est = angle(y(1:Nfft/Nps,1)'*y(Nfft/Nps+1:2*Nfft/Nps,1)).*Nps/(2*pi);
    cfo_est = mean(cfo_est);
elseif strcmpi(method,'moose')
    Nfft = varargin{2};
    Ncp = varargin{3};
    cfo_est = angle(y(:,1)'*y(:,2))./(2*pi);
    cfo_est = cfo_est*Nfft/(Nfft+Ncp);
elseif strcmpi(method,'classen')
    xp = varargin{2};
    Nfft = varargin{3};
    Ncp = varargin{4};
    idx = varargin{5};

    arg = angle((y(:,2:end).*conj(y(:,1:end-1))).'*...
        (conj(xp(:,2:end)).*xp(:,1:end-1)));
    arg = diag(arg).';
    cfo_est = arg./(2*pi*(idx(2:end)-idx(1:end-1)));
    cfo_est = mean(cfo_est).*Nfft./(Nfft+Ncp);
else
    error('unsupported estimation algorithm');
end

% arguments out.
if nargout == 0 || 1
    varargout = {cfo_est};
elseif nargout == 2
    varargout = {cfo_est,0};
end

function varargout = cfo_mtx(varargin)
% Author: Devin - <EMAIL>
% 10/18/2022
% equalization banded matrix for cfo.
% Inputs:
%   cfo      : normalized cfo by subcarrier frequency.
%   N        : FFT point.
%   tau      : bandwidth of banded matrix.
% Outputs:
%   mtx      : banded matrix for qualization.
%   mtx_full : full bandwidth banded matrix.

global ShowFigure;

narginchk(2,3);
nargoutchk(0,2);

% parse input arguments.
cfo = varargin{1};
N = varargin{2};
if nargin == 3
    tau = varargin{3};
else
    tau = -1; % full bandwidth
end

m = 0:N-1;
mtx = zeros(N);
mtx_full = zeros(N);
for k = 0:N-1
    mtx(k+1,:) = sin(pi*(m-k+cfo))./(N*sin(pi*((m-k+cfo)/N)))...
        .*exp(1i*pi*(m-k+cfo)*(N-1)/N);
end

if tau ~= -1
    mtx_full = mtx';
    mtx_banded = tril(ones(N),tau).*triu(ones(N),-tau);
    mtx = mtx.*mtx_banded;
end
mtx = mtx';

if nargout == 0 || 1
    varargout = {mtx};
elseif nargout == 2
    varargout = {mtx,mtx_full};
end
