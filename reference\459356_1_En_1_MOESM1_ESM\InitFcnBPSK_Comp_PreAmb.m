% Init Function of Model BPSK_Com[p
% This function is loaded whenever a simulation is started
% It loads the parameters of the simulation (as specified in the preceding
% PreLoadFcnBPSK_Comp_PreAmb.m) onto the model


% Compute the parameters of the model

fS = OS * fC;  % Define the sampling frequency of the (discrete) model
T = 1/fS;      % sampling interal of the model
fT = 0.4 * fR; % Transit frequency of PLL (Costas loop)
omegaT = 2 * pi * fT;
tau1 = 20e-6;  % integrator time constant in PI loop filter 
K0 = tau1 * omegaT^2; % VCO gain
omega0 = 2 * pi * (fC - delta_f);
tR = 1/fR; % duration of one symbol (bit)
omegaC = 2 * pi * fC;

% Check the ratio of sampling times of the model. This ratio must be
% an integer number. If not, round sampling times to fulfill that
% requirement.

T_string = num2str(T, 3);
% Note: T_string is string variable of sampling time T (rounded)
T_round = str2num(T_string);
tR_string = num2str(tR, 3);
tR_round = str2num(tR_string);
K = round(tR_round/T_round); % round ratio of sampling times to integer
tR_string = num2str(K*T_round, 9);
% Note: tR_string is string variable of sampling time tR (rounded)

% prewarp corner frequencies (for bilinear z transform)
omegaT_P = (2/T) * tan(omegaT * T/2);

% enter parameters into the model

set_param('BPSK_Comp_PreAmb', 'FixedStep', T_string); % sampling time of model
set_param('BPSK_Comp_PreAmb', 'Stop time', num2str((nCycles+nPreAmb) * tR)); 
set_param('BPSK_Comp_PreAmb/RTRand', 'OutPortSampleTime', T_string);
set_param('BPSK_Comp_PreAmb/RTPreAmb', 'OutPortSampleTime', T_string);

set_param('BPSK_Comp_PreAmb/Step1', 'Time', num2str(nPreAmb*tR));
set_param('BPSK_Comp_PreAmb/Step1', 'Sample time', tR_string);

set_param('BPSK_Comp_PreAmb/Carrier', 'Sample time', T_string);
set_param('BPSK_Comp_PreAmb/Carrier', 'Frequency', num2str(omegaC));

set_param('BPSK_Comp_PreAmb/Random', 'Sample time', tR_string);

set_param('BPSK_Comp_PreAmb/F3(z)', 'Numerator', ['[', num2str([1+2/(omegaT_P*T), 1-2/(omegaT_P*T)]), ']']); 
set_param('BPSK_Comp_PreAmb/F3(z)', 'Denominator', ['[', num2str([2*tau1/T, -2*tau1/T]), ']']);
set_param('BPSK_Comp_PreAmb/F3(z)', 'Sample time', T_string);

set_param('BPSK_Comp_PreAmb/F4(z)', 'Numerator', ['[', num2str([0, T]), ']']);
set_param('BPSK_Comp_PreAmb/F4(z)', 'Denominator', ['[', num2str([1, -1]), ']']);
set_param('BPSK_Comp_PreAmb/F4(z)', 'Sample time', T_string);

set_param('BPSK_Comp_PreAmb/K0', 'Value', num2str(K0));

set_param('BPSK_Comp_PreAmb/omega0', 'Value', num2str(omega0));

set_param('BPSK_Comp_PreAmb/Hilbert', 'NumDelays', num2str(OS/4));
