% Start Function of Model QPSK5

% Enter symbol rate fR
fR = input('Enter symbol rate fR [100000]: ');
if isempty(fR)
    fR = 100000;
end

% enter frequency offset delta_f
delta_f = input('Enter initial VCO frequency offset delta_f [10000]: ');
if isempty(delta_f);
    delta_f = 10000;
end

% Enter number of symbols nCycles for simulation
nCycles = input('Enter number of symbols (bits) nCycles [20]: ');
if isempty(nCycles)
    nCycles = 20;
end

% Enter carrier frequency fC
fC = input('Enter carrier frequency fC [400000]: ');
if isempty(fC)
    fC = 400000;
end

% compute the parameters of the model
fS = 4 * fC; % sampling frequency must be at least 4 * carrier frequency
f3dB = 4 * fR; % preliminary: the corner frequency of this filter has an influence
               % on the settling time of the rotated vector. Rotating slowlier
               % leads to a greater number of decision errors.
omega3dB = 2 * pi * f3dB;
fT = f3dB/5;
T = 1/fS; % sampling interal
omegaT = 2 * pi * fT;
tau1 = 20e-6; % integrator time constant in PI loop filter 
K0 = tau1 * omegaT^2; % VCO gain
f0 = fC - delta_f;
omega0 = 2 * pi * f0;
tR = 1/fR; % duration of one symbol (bit)
omegaC = 2 * pi * fC;
tS = 1/(fC * 32); % sampling interval for sine generator, 32 samples per cycle
t0 = 1/(f0 * 32);

% prewarp corner frequencies (for bilinear z transform)
omega3dB_P = (2/T) * tan(omega3dB * T/2);
omegaT_P = (2/T) * tan(omegaT * T/2);

% enter parameters into the model
set_param('QPSK5/RandomI', 'Sample time', num2str(tR));
set_param('QPSK5/RandomQ', 'Sample time', num2str(tR));
set_param('QPSK5/CarrierI', 'Frequency', num2str(omegaC));
set_param('QPSK5/CarrierI', 'Sample time', num2str(tS));
set_param('QPSK5/CarrierQ', 'Frequency', num2str(omegaC));
set_param('QPSK5/CarrierQ', 'Sample time', num2str(tS));
set_param('QPSK5', 'Stop time', num2str(nCycles * tR));
set_param('QPSK5/sin', 'Frequency', num2str(omega0));
set_param('QPSK5/sin', 'Sample time', num2str(t0));
set_param('QPSK5/cos', 'Frequency', num2str(omega0));
set_param('QPSK5/cos', 'Sample time', num2str(t0));
set_param('QPSK5/F1(z)', 'Numerator', ['[', num2str([1, 1]), ']']);
set_param('QPSK5/F1(z)', 'Denominator', ['[', num2str([1+2/(omega3dB_P*T), 1-2/(omega3dB_P*T)]), ']']);
set_param('QPSK5/F1(z)', 'Sample time', num2str(T));
set_param('QPSK5/F2(z)', 'Numerator', ['[', num2str([1, 1]), ']']);
set_param('QPSK5/F2(z)', 'Denominator', ['[', num2str([1+2/(omega3dB_P*T), 1-2/(omega3dB_P*T)]), ']']);
set_param('QPSK5/F2(z)', 'Sample time', num2str(T));
set_param('QPSK5/CounterCLK', 'Sample time', num2str(T));