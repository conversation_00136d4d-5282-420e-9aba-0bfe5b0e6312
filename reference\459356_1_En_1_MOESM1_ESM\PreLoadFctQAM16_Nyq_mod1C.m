% Filename PreLoadFctQAM16_Nyq_mod1C.m

% Preload Function of Model QAM16_Nyq_mod1C.
% This function is executed before loading the model.
% It opens a figure window where the operator can enter
% the parameters of the simulation (using edit controls).
% When this is completed, the operator hits the Done button.
% This loads the content of the edit controls into workspace
% and saves the parameters to the parameter file params_BPSK_Comp.mat.

% Set the constants used in simulation

fs = 100000;    % symbol frequency
ov_c = 4;       % oversampling factor, carrier freq/symbol freq
ov_m = 16;      % oversampling factor of carrier generator.
                % carrier sampling freq/carrier freq
ov_rrcf = 16;   % oversampling factor of RRCF,
                % sampling freq of RRCF/symbol freq
n_hw = 8;       % number of half waves in impulse resonse of RRCF
del = 0;
dfCLK = 0;      % relative clock frequency error in receiver

if exist('paramsmod1C.mat')
    load paramsmod1C; % load parameters from file paramsmod1C.mat
    disp(['Config file found', 10, 13]);
else
    % file not found, load default parameters
    n_syn = 10;
    n_data = 20;
    dfc = 0;
end

% Convert the parameters to strings. These will be entered into the
% edit controls.

n_data_str = num2str(n_data);
n_syn_str = num2str(n_syn);
dfc_str = num2str(dfc);

% Compute the variables used by the simulation

fc = ov_c * fs;          % carrier frequency
omega_c = 2 * pi * fc;
f0 = fs/2;               % Nyquist bandwidth (RRCF filters)
omega_s = 2 * pi * fs;
Ts = 1/fs;
ffix = ov_m * fc;        % model sampling freq
Tfix = 1/ffix;
fs_rrcf = ov_rrcf * fs;  % sampling frequency of RRCF
Ts_rrcf = 1/fs_rrcf;
nSymb = n_data + n_syn;  % total number of symbols

% Create a figure object

flag = 0;
fig1 = figure('Units', 'normalized',...
              'Position', [0.1 0.1 0.6 0.5],...
              'Name', 'Parameters of Simulation');

% Create a static text window displaying operator instructions          
text1 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.05 0.8 0.25 0.08],...
                  'BackgroundColor', [0.5 1 1],...
                  'FontSize', 12);
% Wrap the text to be displayed on more than one line              
Msg_text1 = {'Enter/modify parameters of simulation'...
             'in edit controls.'...
             'Then hit ''Done'' button'};
[WrapString1, newpdfc1] = textwrap(text1, Msg_text1);
set(text1,'String',WrapString1,'Position',newpdfc1);

% create 3 static text windows + 3 edit controls
% static text names the parameters to be entered into edit controls
text2 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.9 0.3 0.04],...
                  'String', 'Number of symbols in data sequence');
          
edit2 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.85 0.3 0.04],...
                  'Style', 'edit',...
                  'String', n_data_str,...
                  'BackgroundColor', 'y');

text3 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.77 0.3 0.04],...
                  'String', 'Number of symbols in preamble');
          
edit3 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.72 0.3 0.04],...
                  'Style', 'edit',...
                  'String', n_syn_str,...
                  'BackgroundColor', 'y');

text4 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.64 0.3 0.04],...
                  'String', 'Frequency error [Hz]');
          
edit4 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.59 0.3 0.04],...
                  'Style', 'edit',...
                  'String', dfc_str,...
                  'BackgroundColor', 'y');
              
% Create a pushbutton 'Done'
% This button must be hit at the end of parameter specification.
% Callback function tests validity of numeric entries.
% An error message is displayed in static text window text7 when
% the operator entered an invalid number.
% When ok, parameters are saved to file.

but1 = uicontrol('Parent', fig1,...
                 'Units', 'normalized',...
                 'Position', [0.6 0.2 0.15 0.05],...
                 'Style', 'pushbutton',...
                 'String', 'Done',...
                 'Callback', ['n_data_str = get(edit2, ''String'');'...
                              'n_data = str2num(n_data_str);'...
                              'n_syn_str = get(edit3, ''String'');'...
                              'n_syn = str2num(n_syn_str);'...
                              'dfc_str = get(edit4, ''String'');'...
                              'dfc = str2num(dfc_str);'...
                              'flag = 0;'...
                              'if isempty(n_data) || isempty(n_syn) || isempty(dfc),'...
                              '         flag = 1;'...
                              'end;'...
                              'if flag == 1,'...
                              '   set(text7, ''Visible'', ''on'');'...
                              'else'...
                              '   set(text7, ''Visible'', ''off'');'...
                              '   n_data = str2num(n_data_str);'...
                              '   n_syn = str2num(n_syn_str);'...
                              '   dfc = str2num(dfc_str);'...
                              '   nSymb = n_data + n_syn;'...
                              '   save paramsmod1C n_data n_syn dfc;'...
                              'end;']);

% Create a pushbutton that can be used to enter initial default values
% for all parameters.
but2 = uicontrol('Parent', fig1,...
                 'Units', 'normalized',...
                 'Position', [0.6 0.1 0.15 0.05],...
                 'Style', 'pushbutton',...
                 'String', 'Set init defaults',...
                 'Callback', ['n_data = 20;'...
                              'n_syn = 10;'...
                              'dfc = 0;'...
                              'set(edit2, ''String'', num2str(n_data));'...
                              'set(edit3, ''String'', num2str(n_syn));'...
                              'set(edit4, ''String'', num2str(dfc));'...
                              ]);

text7 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.05 0.3 0.25 0.08],...
                  'Visible', 'off',...
                  'BackgroundColor', 'r');
              
Msg_text7 = {'Error: invalid numeric format!'... 
             'Please correct entry(ies)'...
             'and hit ''Done'' button again.'};
[WrapString7, newpos7] = textwrap(text7, Msg_text7);
set(text7,'String',WrapString7,'Position',newpos7);


