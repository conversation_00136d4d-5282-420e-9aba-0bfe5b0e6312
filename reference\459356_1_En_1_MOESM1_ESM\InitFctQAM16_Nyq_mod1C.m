% Filename: InitFctQAM16_Nyq_mod1C
%
% This is the initalization function of model QAM16_Nyq_mod1C

% Parameters of the model
% -------------------------------------------------------
% ov_c      oversampling factor, carrier freq/symbol freq
% ov_m      oversampling factor of carrier generator,
%           carrier samp freq/carrier freq
% ov_rrcf   oversampling factor of RRCFs,
%           samp freq of RRCF/symbol freq
% nSymb     number of symbols in simulation
% n_syn     number of symbols in initialization preamble
% n_hw      number of half waves in impulse responmse of RRCF
% dfc       freq error of local carrier freq in receiver
% dfCLK     relative clock frequency error (in receiver)

% Variables of the model
% ----------------------
% fs          symbol frequency = 100 kHz
% fc          carrier frequency = 400 kHz
% f0          Nyquist bandwidth of RRCF's, default = fs/2 = 50 kHz
% Ts          1/fs symbol period
% ffix        ffix = ov_m * fc, sampling frequency  in carrier generator,
%             default = 6.4 MHz
% Tfix        Tfix = 1/ffix
% fs_rrcf     fs_rrcf = ov_rrcf * fs, sampling frequency of RRCFs
% Ts_rrcf     Ts_rrcf = 1/fs_rrcf
% n_rrcf      n_rrcf = n_hw * ov_rrcf, length of RRCFs
% r           excess bandwidth of RRCF (one-sided)

% *********************************************************************

% Initialize parameters of subsystem TX
%
% Design the RRCF filter

n_rrcf = n_hw * ov_rrcf; % length of RRCF
r = 0.2;     % excess bandwidth of RRCF
t = (-n_rrcf/2:n_rrcf/2)/fs_rrcf + eps;   % time vector, eps added to avoid 0/0 in following computations
b1 = sin(pi*t*(1-r)/Ts) + (4*r*t/Ts) .* cos(pi*t*(1+r)/Ts);
b2 = b1 ./ ((pi*t/Ts) .* (1  - (4*r*t/Ts).^2));
a_rrcf = [1 zeros(1, n_rrcf)];
num_rrcf_text = ['[', num2str(b2), ']'];
den_rrcf_text = ['[', num2str(a_rrcf), ']'];

% Int = integrator
num_Int = Tfix*[1 0];
den_Int = [1 -1];
num_Int_text = ['[', num2str(num_Int), ']'];
den_Int_text = ['[', num2str(den_Int), ']'];
%
% Initialize parameters of subsystem Demod
% Note: Demod uses the same integrator as TX
%
% Setup parameters of PLL (used for carrier recovery)
% Note: this PLL contains a large dead time given by the RRCF (RRCFI and
% RRCFQ), which causes a stability problem. We first compute the radian
% frequency where the phase shift created by the RRCF is - 45 deg (-pi/4). 
% This radian frequency will be called omega_n. Then we set the break
% frequency of the PI filter F3(z) at omega_n/3. When doing so the phase
% shift of the PI filter is -45 deg at omega_n and around -20 deg at
% omega_n/3. Hence the total phase shift at omega_n becomes -110 - 45 =
% -155 deg, which leads to a phase margin of 25 deg, which is considered
% sufficient for stability.
%
f_LO = fc + dfc; % receiver carrier frequency
omega_n = pi/(2 * n_hw *Ts);   % natural radian freq of PLL
tau2 = 3/omega_n;
tau1 = 100e-6;   % time constants of loop filter is chosen arbitrarily
Kd = 1;          % phase detector gain of PLL
% The VCO gain K0 is chosen such that the open loop gain becomes 1 at
% omega_n.
K0 = omega_n^2 * tau1/(3*Kd);    % VCO gain
omega_LO = 2 * pi * f_LO;
num_F3 = [1 + 2 * tau2/Tfix, 1 - 2 * tau2/Tfix];
den_F3 = (2 * tau1/Tfix) * [1, -1];
num_F3_text = ['[', num2str(num_F3), ']'];
den_F3_text = ['[', num2str(den_F3), ']'];
%
% Setup parameters of subsystem Prefilt
%
% Design the RRCF operating at 2 * fs
b_rrcf2 = b2(1:ov_rrcf/2:n_hw*ov_rrcf+1)/2;
a_rrcf2 = [1 zeros(1, 2*n_hw)];
num_rrcf2_text = ['[', num2str(b_rrcf2), ']'];
den_rrcf2_text = ['[', num2str(a_rrcf2), ']'];
% Design the prefilter
n_pre = 2 * n_hw;  % length of prefilte - 1
fs_pre = 2 * fs;   % sampling frequency of prefilter
t_pre = (-n_pre/2:n_pre/2)/fs_pre + eps;  % time vector for prefilter
b_rcf = (sin(pi*t_pre/Ts) .* cos(pi*r*t_pre/Ts) ./ ((pi*t_pre/Ts) ...
        .* (1-(2*r*t_pre/Ts).^2)))/2;
b_pre = 2 * b_rcf .* cos(2 * pi * fs * t_pre);
a_pre = [1, zeros(1, n_pre)];
num_pre_text = ['[', num2str(b_pre), ']'];
den_pre_text = ['[', num2str(a_pre), ']'];
%
% Setup parameters of subsystem Sync
%
% Design the interpolation filter
% Sampling frequency of interpolation filter = ov_rrcf * fs
% Filter bandwidth = fs
% Length = 4 * ov_rrcf + 1;
% For these parameters, the filter delay is 2 * Ts
b_interpol = fir1(4*ov_rrcf, 2/ov_rrcf) * ov_rrcf/2;
a_interpol = [1, zeros(1, 4*ov_rrcf)];
num_interpol_text = ['[', num2str(b_interpol), ']'];
den_interpol_text = ['[', num2str(a_interpol), ']'];
% design the bandpass filter
delta_f = 0.01 * fs;   % one-sided bandwidth set 1 kHz
wn_BP = [fs - delta_f, fs + delta_f] * 2/(ov_rrcf * fs);
[b_BP, a_BP] = butter(2, wn_BP);
num_BP_text = ['[', num2str(b_BP, 12), ']'];
den_BP_text = ['[', num2str(a_BP, 12), ']'];
% Note: because the poles of the bandpass filter are very close to the unit
% circle, the num2str conversion must be made with a higher precision than
% standard (4 digits for fractional part).
%
% Design the PLL
%
% Note: the parameters calculated below are also used in new subsystem
% Sync.
%
% N_SS = ratio of center frequency f0 to transit frequency fT (freq where
% open-loop gain = 1. When N is chosen too small, the system becomes
% unstable. When it is too large, the PLL response is sluggish. N = 10 is a
% good compromise.
N_SS = 10;
Kd_SS = 1/pi; % phase detector (EXOR) gain
f0_SS = 100000; % center frequency of PLL
omega_0_SS = 2 * pi * f0_SS; % radian center frequency
K0_SS = 2 * pi * f0_SS; % VCO gain; this parameter can be chosen free
% now design the loop filter; its transfer function is 
% F(s) = (1+s tau2)/s tau1
tau1_SS = N_SS^2 * Kd_SS/omega_0_SS; 
tau2_SS = N_SS/omega_0_SS;
% transform transfer function into z domain (using bilinear z transform)
num_LF_SS = [1 + 2 * tau2_SS/Tfix, 1 - 2 * tau2_SS/Tfix];
den_LF_SS = (2 * tau1_SS/Tfix) * [1, -1];
num_LF_SS_text = ['[', num2str(num_LF_SS), ']'];
den_LF_SS_text = ['[', num2str(den_LF_SS), ']'];

% Setup the parameters of subsystem AGC

Ti_AGC = 0.00004;  % This parameters was initially chosen 0.00001. It showed
                   % up, however, that the response of the AGC loop became
                   % too noisy which lead to instabilities, hence Ti_AGC was
                   % made larger to get smoother loop response.
num_H_AGC = [1 1]; % lowpass filter of AGC loop is designed as an I controller 
den_H_AGC = (2*Ti_AGC/Tfix)*[1 -1];
num_H_AGC_text = ['[', num2str(num_H_AGC), ']'];
den_H_AGC_text = ['[', num2str(den_H_AGC), ']'];

% Setup parameters of subsystem Rot

% Determine the parameters of the PhLL system
% Set transit frequency of PhLL to 50 kHz
omega2_rot = 2*pi*50000;
tau2_rot = 1/omega2_rot; % locate zero of loop filter at omega2_rot
tau1_rot = 10e-6; % arbitrarily chosen
Kd_rot = 1; % phase detector gain
K0_rot = omega2_rot^2 * tau1_rot/Kd_rot; % DCO gain chosen such that open loop
                                         % gain of PLL is 1 at omega2_rot
% Setup parameters of loop filter
% Note: loop filter is a PI filter
num_LF_rot = [1 + 2*tau2_rot/Tfix, 1 - 2*tau2_rot/Tfix];
den_LF_rot = 2*tau1_rot/Tfix * [1 -1];
num_LF_rot_text = ['[', num2str(num_LF_rot), ']'];
den_LF_rot_text = ['[', num2str(den_LF_rot), ']'];

%Setup parameters of DCO (integrator)
num_Int_rot = Tfix * K0_rot * [1 0];  % corrected statement
den_Int_rot = [1 -1];
num_Int_rot_text = ['[', num2str(num_Int), ']'];
den_Int_rot_text = ['[', num2str(den_Int), ']'];


% set parameters of model

set_param('QAM16_Nyq_mod1C', 'FixedStep', num2str(Tfix));
set_param('QAM16_Nyq_mod1C', 'Stop time', num2str(nSymb * Ts)); 
%
% set parameters of subsystem TX

set_param('QAM16_Nyq_mod1C/TX/RTII', 'OutPortSampleTime', num2str(Ts_rrcf));
set_param('QAM16_Nyq_mod1C/TX/RTIQ', 'OutPortSampleTime', num2str(Ts_rrcf));
set_param('QAM16_Nyq_mod1C/TX/RTISyn', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/TX/RTISynDel', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/TX/RTIEnable', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/TX/RTIDelta', 'OutPortSampleTime', num2str(Ts_rrcf));
set_param('QAM16_Nyq_mod1C/TX/RTIcos', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/TX/RTIsin', 'OutPortSampleTime', num2str(Tfix));

set_param('QAM16_Nyq_mod1C/TX/RRCFI', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/TX/RRCFI', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/TX/RRCFI', 'Sample time', num2str(Ts_rrcf));
set_param('QAM16_Nyq_mod1C/TX/RRCFI', 'Numerator', num_rrcf_text);
set_param('QAM16_Nyq_mod1C/TX/RRCFI', 'Denominator', den_rrcf_text);

set_param('QAM16_Nyq_mod1C/TX/RRCFQ', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/TX/RRCFQ', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/TX/RRCFQ', 'Sample time', num2str(Ts_rrcf));
set_param('QAM16_Nyq_mod1C/TX/RRCFQ', 'Numerator', num_rrcf_text);
set_param('QAM16_Nyq_mod1C/TX/RRCFQ', 'Denominator', den_rrcf_text);

set_param('QAM16_Nyq_mod1C/TX/RandI', 'Minimum', num2str(-2));
set_param('QAM16_Nyq_mod1C/TX/RandI', 'Maximum', num2str(1.99));
set_param('QAM16_Nyq_mod1C/TX/RandI', 'Sample time', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/RandQ', 'Minimum', num2str(-2));
set_param('QAM16_Nyq_mod1C/TX/RandQ', 'Maximum', num2str(1.99));
set_param('QAM16_Nyq_mod1C/TX/RandQ', 'Sample time', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/IntC', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/TX/IntC', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/TX/IntC', 'Sample time', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/TX/IntC', 'Numerator', num_Int_text);
set_param('QAM16_Nyq_mod1C/TX/IntC', 'Denominator', den_Int_text);
set_param('QAM16_Nyq_mod1C/TX/omega_c', 'Sample time', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/TX/omega_c', 'Value', num2str(omega_c));
set_param('QAM16_Nyq_mod1C/TX/St1', 'Sample time', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/St2', 'Sample time', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/St2', 'Time', num2str(n_syn*Ts));
set_param('QAM16_Nyq_mod1C/TX/St3', 'Sample time', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/St3', 'Time', num2str((n_syn+n_hw)*Ts));
set_param('QAM16_Nyq_mod1C/TX/St4', 'Time', num2str((n_hw+1)*Ts));

set_param('QAM16_Nyq_mod1C/TX/Del1', 'NumDelays', num2str(n_rrcf+2));
set_param('QAM16_Nyq_mod1C/TX/Del2', 'NumDelays', num2str(n_rrcf+2));
set_param('QAM16_Nyq_mod1C/TX/Del3', 'NumDelays', num2str(2));

set_param('QAM16_Nyq_mod1C/TX/Delta', 'Period', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/Delta', 'SampleTime', num2str(Ts_rrcf));
set_param('QAM16_Nyq_mod1C/TX/Delta', 'PulseWidth', num2str(100/ov_rrcf));

set_param('QAM16_Nyq_mod1C/TX/Rect', 'Sample time', num2str(Ts));
set_param('QAM16_Nyq_mod1C/TX/Rect', 'Period', num2str(2*Ts));
set_param('QAM16_Nyq_mod1C/TX/Rect', 'PulseWidth', num2str(50));
set_param('QAM16_Nyq_mod1C/TX/Rect', 'Amplitude', num2str(1));

set_param('QAM16_Nyq_mod1C/TX/RTIRect', 'OutPortSampleTime', num2str(Ts));

% set parameters of subsystem Demod0 in Demod_P

set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/F3(z)', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/F3(z)', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/F3(z)', 'Numerator', num_F3_text);
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/F3(z)', 'Denominator', den_F3_text);
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/F3(z)', 'Sample time', num2str(Tfix));
%
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/Int', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/Int', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/Int', 'Numerator', num_Int_text);
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/Int', 'Denominator', den_Int_text);
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/Int', 'Sample time', num2str(Tfix));
%
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/K0', 'Value', num2str(K0));
set_param('QAM16_Nyq_mod1C/Demod_P/Demod0/omega_LO', 'Value', num2str(omega_LO));
%
% set parameters of subsystem Demod1 in Demod_P
%
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2I', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2I', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2I', 'Numerator', num_rrcf2_text);
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2I', 'Denominator', den_rrcf2_text);
%
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2Q', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2Q', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2Q', 'Numerator', num_rrcf2_text);
set_param('QAM16_Nyq_mod1C/Demod_P/Demod1/RRCF2Q', 'Denominator', den_rrcf2_text);
%
% set parameters of subsystem Demod2 in Demod_P
%
set_param('QAM16_Nyq_mod1C/Demod_P/Demod2/RTID', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/Demod_P/Demod2/RTQD', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/Demod_P/Demod2/RTDelta', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/Demod_P/Demod2/RTI_del', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/Demod_P/Demod2/RTQ_del', 'OutPortSampleTime', num2str(Tfix));
%
% set parameters of subsystem CLKRec

% set parameters of subsystem Prefilt
%
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/RRCF2', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/RRCF2', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/RRCF2', 'Numerator', num_rrcf2_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/RRCF2', 'Denominator', den_rrcf2_text);

set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/PreFilt', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/PreFilt', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/PreFilt', 'Numerator', num_pre_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Prefilt/PreFilt', 'Denominator', den_pre_text);
%
% set parameters of subsystem Sync
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Interpol', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Interpol', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Interpol', 'Numerator', num_interpol_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Interpol', 'Denominator', den_interpol_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Interpol', 'Sample time', num2str(Ts_rrcf));

set_param('QAM16_Nyq_mod1C/CLKRec/Sync/BP', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/BP', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/BP', 'Numerator', num_BP_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/BP', 'Denominator', den_BP_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/BP', 'Sample time', num2str(Ts_rrcf));

set_param('QAM16_Nyq_mod1C/CLKRec/Sync/RTDelta', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/RTu1', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/RTCLK', 'OutPortSampleTime', num2str(Ts_rrcf));

set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Hlf(z)', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Hlf(z)', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Hlf(z)', 'Numerator', num_LF_SS_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Hlf(z)', 'Denominator', den_LF_SS_text);
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/Hlf(z)', 'Sample time', num2str(Tfix));

set_param('QAM16_Nyq_mod1C/CLKRec/Sync/*K0', 'Gain', num2str(K0_SS));
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/omega_0', 'Value', num2str(omega_0_SS));
set_param('QAM16_Nyq_mod1C/CLKRec/Sync/*Tfix', 'Gain', num2str(Tfix));

set_param('QAM16_Nyq_mod1C/CLKRec/ClockGen/Delta0', 'Value', num2str((1+dfCLK)/(ov_c*ov_rrcf)));

% set parameters of subsystem AGC_P
set_param('QAM16_Nyq_mod1C/AGC_P/H(z)', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/AGC_P/H(z)', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/AGC_P/H(z)', 'Numerator', num_H_AGC_text);
set_param('QAM16_Nyq_mod1C/AGC_P/H(z)', 'Denominator', den_H_AGC_text);
set_param('QAM16_Nyq_mod1C/AGC_P/H(z)', 'Sample time', num2str(Tfix));

% set parameters of subsystem Rot_P
set_param('QAM16_Nyq_mod1C/Rot_P/LF', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/Rot_P/LF', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/Rot_P/LF', 'Numerator', num_LF_rot_text);
set_param('QAM16_Nyq_mod1C/Rot_P/LF', 'Denominator', den_LF_rot_text);
set_param('QAM16_Nyq_mod1C/Rot_P/LF', 'Sample time', num2str(Tfix));

set_param('QAM16_Nyq_mod1C/Rot_P/DCO', 'Numerator', '[]');
set_param('QAM16_Nyq_mod1C/Rot_P/DCO', 'Denominator', '[]');
set_param('QAM16_Nyq_mod1C/Rot_P/DCO', 'Numerator', num_Int_rot_text);
set_param('QAM16_Nyq_mod1C/Rot_P/DCO', 'Denominator', den_Int_rot_text);
set_param('QAM16_Nyq_mod1C/Rot_P/DCO', 'Sample time', num2str(Tfix));

% set parameters of subsystem Estim
set_param('QAM16_Nyq_mod1C/Estim/RTI_del', 'OutPortSampleTime', num2str(Tfix));
set_param('QAM16_Nyq_mod1C/Estim/RTQ_del', 'OutPortSampleTime', num2str(Tfix));

