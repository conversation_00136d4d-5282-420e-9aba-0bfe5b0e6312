import numpy as np
import matplotlib.pyplot as plt
import utility


class Constellation:
    def __init__(self, bits_per_symbol, constellation):
        self.bits_per_symbol = bits_per_symbol
        self.constellation = constellation


    def map_bits_to_symbols(self, bits):
        remainder = len(bits) % self.bits_per_symbol
        if remainder != 0:
            bits += [0] * (self.bits_per_symbol - remainder)
        symbols = []
        for i in range(0, len(bits), self.bits_per_symbol):
            symbol_bits = bits[i:i + self.bits_per_symbol]
            symbol_index = int(''.join(map(str, symbol_bits)), 2)
            symbols.append(self.constellation[symbol_index])
        return symbols


    def judge_symbol(self, symbol):
        distances = [np.abs(symbol - point) for point in self.constellation]
        return np.argmin(distances)


    def judge_symbol_as_bits(self, symbol):
        symbol_index = self.judge_symbol(symbol)
        symbol_bits = format(symbol_index, '04b')
        return list(map(int, symbol_bits))


    def judge_symbol_as_symbol(self, symbol):
        symbol_index = self.judge_symbol(symbol)
        return self.constellation[symbol_index]


class QAM16(Constellation):
    def __init__(self):
        self.bits_per_symbol = 4
        self.constellation = [
            -3 + 3j, -3 + 1j, -3 - 3j, -3 - 1j,
            -1 + 3j, -1 + 1j, -1 - 3j, -1 - 1j,
            3 + 3j, 3 + 1j, 3 - 3j, 3 - 1j,
            1 + 3j, 1 + 1j, 1 - 3j, 1 - 1j
        ] / np.sqrt(10)


class ReceiverState:
    IDLE = 0
    S0 = 1
    S1 = 2
    BARKER = 3
    MESSAGE = 4
    GUARD_INTERVAL = 5


class BasebandTransceiver:
    def __init__(self, transmit_sps=4, receive_sps=4, rrc_filter_symbols=8, rolloff_factor=0.35):
        self.transmit_sps = transmit_sps
        self.receive_sps = round(receive_sps)
        self.rrc_filter_symbols = rrc_filter_symbols
        self.rolloff_factor = rolloff_factor

        self.transmit_filter = self.rrc(self.transmit_sps)
        self.receive_filter = self.rrc(self.receive_sps) / self.receive_sps
        self.receive_filter_state = np.zeros(len(self.receive_filter), dtype=complex)

        self.energy_detect_symbol = 2
        self.S0_symbol = 10
        self.S1_symbol = 18
        self.barker_symbol = 7

        self.barker_code = [1, 1, 1, -1, -1, 1, -1]
        self.preamble = [1] * (self.energy_detect_symbol + self.S0_symbol)
        self.preamble += [1, -1] * (self.S1_symbol // 2) + ([1] if self.S1_symbol % 2 == 1 else [])
        self.preamble += self.barker_code
        self.constellation = QAM16()

        self.receiver_state = ReceiverState.IDLE
        self.receiver_sample_count = 0

        # warmup_samples = round(10 * self.receive_sps)
        # consecutive_threshold = round(self.energy_detect_symbol * self.receive_sps)
        # self.signal_detector = SignalDetector(warmup_samples, consecutive_threshold)
        self.signal_detector = SignalDetector2(round(self.energy_detect_symbol * self.receive_sps), 25)
        self.agc = AGC()
        self.sample_freq_sync = CostasLoop()
        self.symbol_freq_sync = CostasLoop()
        self.clock_sync = ClockSync()
        self.clock_sync_start = False
        self.barker_history = []
        self.barker_threshold = 5
        self.barker_wait_samples = round((self.barker_symbol + 10) * self.receive_sps)
        self.end_of_frame_threshold = 0.05
        self.guard_interval = 100


    def rrc(self, sps):
        a = self.rolloff_factor
        l = round(self.rrc_filter_symbols * sps)
        if l % 2 == 0:
            l += 1
        t = np.arange(-(l - 1) / 2, (l + 1) / 2) / sps
        h = np.zeros(len(t))
        for i in range(len(t)):
            if t[i] == 0:
                h[i] = 1 - a + (4 * a / np.pi)
            elif abs(t[i]) == 1 / (4 * a):
                h[i] = (a / np.sqrt(2)) * ((1 + 2 / np.pi) * np.sin(np.pi / (4 * a)) + (1 - 2 / np.pi) * np.cos(np.pi / (4 * a)))
            else:
                h[i] = (np.sin(np.pi * t[i] * (1 - a)) + 4 * a * t[i] * np.cos(np.pi * t[i] * (1 + a)))
                h[i] /= (np.pi * t[i] * (1 - (4 * a * t[i]) ** 2))
        return h


    def transmit(self, data):
        message = self.constellation.map_bits_to_symbols(data)
        symbols = np.array(self.preamble + message)

        num_symbols = len(symbols)
        num_samples = int(num_symbols * self.transmit_sps) + 1
        impulse_series = np.zeros(num_samples, dtype=complex)
        for i in range(num_symbols):
            impulse_series[round(i * self.transmit_sps)] = symbols[i]

        baseband_signal = np.convolve(impulse_series, self.transmit_filter)
        return baseband_signal


    def receive(self, signal):
        data = []
        filtered_samples = []
        receiver_states = []
        sample_freq_offsets = []
        freq_adjusted_samples = []
        clock_offsets = []
        magnitudes = []
        symbols = []
        for sample in signal:
            receiver_states.append(self.receiver_state)
            bits, filtered_sample, sample_freq_offset, freq_adjusted_sample, clock_offset, magnitude, symbol = self.process(sample)
            if bits is not None:
                data.extend(bits)
            if filtered_sample is not None:
                filtered_samples.append(filtered_sample)
            if sample_freq_offset is not None:
                sample_freq_offsets.append(sample_freq_offset)
            if freq_adjusted_sample is not None:
                freq_adjusted_samples.append(freq_adjusted_sample)
            if clock_offset is not None:
                clock_offsets.append(clock_offset)
            if magnitude is not None:
                magnitudes.append(magnitude)
            if symbol is not None:
                symbols.append(symbol)
        return data, receiver_states, filtered_samples, sample_freq_offsets, freq_adjusted_samples, clock_offsets, magnitudes, symbols


    def process(self, sample):
        self.receive_filter_state = np.roll(self.receive_filter_state, -1)
        self.receive_filter_state[-1] = sample

        bits = None
        filtered_sample = None
        sample_freq_offset = None
        freq_adjusted_sample = None
        clock_offset = None
        magnitude = None
        symbol = None

        if self.receiver_state == ReceiverState.IDLE:
            signal_detected = self.signal_detector.process(sample)
            if signal_detected:
                self.receiver_state = ReceiverState.S0
                self.signal_detector.reset()
                self.sample_freq_sync.reset()
                self.symbol_freq_sync.reset()
                self.clock_sync.reset()
                self.agc.reset()
                # self.sample_freq_sync.phase = np.angle(filtered_sample)

        elif self.receiver_state == ReceiverState.S0:
            filtered_sample = np.dot(self.receive_filter, self.receive_filter_state)

            # stop = round((self.S0_symbol - 1) * self.receive_sps)
            # phase_detector = "monotone" if self.receiver_sample_count <= stop else None
            phase_detector = "monotone"

            freq_adjusted_sample, loop_filter_out = self.sample_freq_sync.process(filtered_sample, phase_detector)
            sample_freq_offset = loop_filter_out / 2 / np.pi * self.receive_sps

            self.receiver_sample_count += 1
            finish = round(self.S0_symbol * self.receive_sps)
            if self.receiver_sample_count == finish:
                self.receiver_state = ReceiverState.S1
                self.receiver_sample_count = 0

        elif self.receiver_state == ReceiverState.S1:
            filtered_sample = np.dot(self.receive_filter, self.receive_filter_state)

            freq_adjusted_sample, loop_filter_out = self.sample_freq_sync.process(filtered_sample)
            sample_freq_offset = loop_filter_out / 2 / np.pi * self.receive_sps

            # symbol_sync_start = round(1 * self.receive_sps)
            # symbol_sync_detect = True if self.receiver_sample_count > symbol_sync_start else False

            if np.real(freq_adjusted_sample) < 0:
                self.clock_sync_start = True

            symbol, clock_offset = self.clock_sync.process(freq_adjusted_sample, detect=self.clock_sync_start)
            if symbol is not None:
                symbol, magnitude = self.agc.process(symbol, detect=True)
                symbol, loop_filter_out = self.symbol_freq_sync.process(symbol, phase_detector="bpsk")
                sample_freq_offset += loop_filter_out / 2 / np.pi

            self.receiver_sample_count += 1
            finish = round((self.S1_symbol - 2) * self.receive_sps)
            if self.receiver_sample_count == finish:
                self.receiver_state = ReceiverState.BARKER
                self.receiver_sample_count = 0
                self.clock_sync_start = False

        elif self.receiver_state == ReceiverState.BARKER:
            filtered_sample = np.dot(self.receive_filter, self.receive_filter_state)

            freq_adjusted_sample, loop_filter_out = self.sample_freq_sync.process(filtered_sample)
            sample_freq_offset = loop_filter_out / 2 / np.pi * self.receive_sps

            symbol, clock_offset = self.clock_sync.process(freq_adjusted_sample)
            if symbol is not None:
                symbol, magnitude = self.agc.process(symbol)
                symbol, loop_filter_out = self.symbol_freq_sync.process(symbol, phase_detector="bpsk")
                sample_freq_offset += loop_filter_out / 2 / np.pi
                self.barker_history.append(symbol)
                if len(self.barker_history) > self.barker_symbol:
                    self.barker_history.pop(0)
                if len(self.barker_history) == self.barker_symbol:
                    correlation = np.dot(np.real(self.barker_history), self.barker_code)
                    if correlation > self.barker_threshold:
                        self.receiver_state = ReceiverState.MESSAGE
                        self.barker_history = []

            self.receiver_sample_count += 1
            if self.receiver_sample_count >= self.barker_wait_samples:
                self.receiver_state = ReceiverState.IDLE
                self.receiver_sample_count = 0
                self.barker_history = []

        elif self.receiver_state == ReceiverState.MESSAGE:
            filtered_sample = np.dot(self.receive_filter, self.receive_filter_state)

            # message_end = self.signal_detector.process(filtered_sample, mode="end")
            # if message_end:
            #     self.receiver_state = ReceiverState.GUARD_INTERVAL
            #     self.signal_detector.reset()

            freq_adjusted_sample, loop_filter_out = self.sample_freq_sync.process(filtered_sample)
            sample_freq_offset = loop_filter_out / 2 / np.pi * self.receive_sps

            symbol, clock_offset = self.clock_sync.process(freq_adjusted_sample)
            if symbol is not None:
                symbol, magnitude = self.agc.process(symbol)
                energy = np.abs(symbol) ** 2
                if energy < self.end_of_frame_threshold:
                    self.receiver_state = ReceiverState.GUARD_INTERVAL
                else:
                    symbol, loop_filter_out = self.symbol_freq_sync.process(symbol, phase_detector="qam")
                    sample_freq_offset += loop_filter_out / 2 / np.pi
                    bits = self.constellation.judge_symbol_as_bits(symbol)

        elif self.receiver_state == ReceiverState.GUARD_INTERVAL:
            self.receiver_sample_count += 1
            if self.receiver_sample_count >= self.guard_interval:
                self.receiver_state = ReceiverState.IDLE
                self.receiver_sample_count = 0

        return bits, filtered_sample, sample_freq_offset, freq_adjusted_sample, clock_offset, magnitude, symbol


    # def process2(self, signal):
    #     filtered_signal = np.convolve(signal, self.receive_filter)
    #     detected_signal = self.detect_signal(filtered_signal, self.receive_sps)
    #     if detected_signal is None:
    #         return None

    #     adjusted_signal = np.zeros(len(detected_signal), dtype=complex)
    #     freq_offset = np.zeros(len(detected_signal), dtype=complex)
    #     clock_offset = np.zeros(len(detected_signal), dtype=complex)

    #     agc = AGC()
    #     sample_freq_sync = CostasLoop(loop_bandwidth=0.1)
    #     monotone_stage_start = 0
    #     monotone_stage_stop = round((self.S0_symbol - 1) * self.receive_sps)
    #     for i in range(monotone_stage_start, monotone_stage_stop):
    #         adjusted_signal[i], loop_filter_out = sample_freq_sync.process(detected_signal[i], phase_detector="monotone")
    #         # adjusted_signal[i] = agc.process(adjusted_signal[i], detect=True)
    #         freq_offset[i] = loop_filter_out / 2 / np.pi * self.receive_sps
    #     for i in range(monotone_stage_stop, len(detected_signal)):
    #         adjusted_signal[i], loop_filter_out = sample_freq_sync.process(detected_signal[i])
    #         freq_offset[i] = loop_filter_out / 2 / np.pi * self.receive_sps

    #     adjusted_symbols = []
    #     symbol_freq_sync = CostasLoop()
    #     clock_sync = ClockSync(K=0.5)
    #     S1_stage_start = round(self.S0_symbol * self.receive_sps)
    #     S1_stage_stop = round((self.S0_symbol + self.S1_symbol - 2) * self.receive_sps)
    #     for i in range(S1_stage_start, S1_stage_stop, self.receive_sps):
    #         adjusted_symbol, clock_offset[i] = clock_sync.process(adjusted_signal[i-1:i+6], detect=True)
    #         adjusted_symbol = agc.process(adjusted_symbol, detect=True)
    #         adjusted_symbol, loop_filter_out = symbol_freq_sync.process(adjusted_symbol, phase_detector="bpsk")
    #         adjusted_symbols.append(adjusted_symbol)
    #         freq_offset[i] += loop_filter_out / 2 / np.pi

    #     message = []
    #     start_message = False
    #     for i in range(S1_stage_stop, len(adjusted_signal)-6, self.receive_sps):
    #         adjusted_symbol, clock_offset[i] = clock_sync.process(adjusted_signal[i-1:i+6])
    #         if np.abs(adjusted_symbol) ** 2 < self.energy_threshold:
    #             break
    #         adjusted_symbol = agc.process(adjusted_symbol)
    #         if not start_message:
    #             adjusted_symbol, loop_filter_out = symbol_freq_sync.process(adjusted_symbol, phase_detector="bpsk")
    #             adjusted_symbols.append(adjusted_symbol)
    #             freq_offset[i] += loop_filter_out / 2 / np.pi
    #             correlation = np.dot(np.real(adjusted_symbols[-self.barker_symbol:]), self.barker_code)
    #             if correlation > self.barker_threshold:
    #                 start_message = True
    #                 print("Start message detected at index:", i)
    #         else:
    #             adjusted_symbol, loop_filter_out = symbol_freq_sync.process(adjusted_symbol, phase_detector="qam")
    #             adjusted_symbols.append(adjusted_symbol)
    #             freq_offset[i] += loop_filter_out / 2 / np.pi
    #             message.extend(QAM16().judge_symbol_as_bits(adjusted_symbol))

    #     utility.plot_sampled_signal(agc.magnitude, title="AGC Magnitude")

    #     return adjusted_signal, adjusted_symbols, freq_offset, clock_offset, message


class SignalDetector:
    def __init__(self, warmup_samples, consecutive_threshold):
        self.warmup = True
        self.warmup_samples = warmup_samples
        self.threshold = 0
        self.consecutive_count = 0
        self.consecutive_threshold = consecutive_threshold


    def process(self, sample):
        energy = np.abs(sample) ** 2
        if self.warmup:
            self.threshold = max(self.threshold, energy)
            self.consecutive_count += 1
            if self.consecutive_count >= self.warmup_samples:
                self.threshold = self.threshold * 4
                self.consecutive_count = 0
                self.warmup = False
        else:
            if energy > self.threshold:
                self.consecutive_count += 1
                if self.consecutive_count >= self.consecutive_threshold:
                    self.consecutive_count = 0
                    return True
            else:
                self.consecutive_count = 0
        return False


    def reset(self):
        self.warmup = True
        self.threshold = 0
        self.consecutive_count = 0


class SignalDetector2:
    def __init__(self, window_length, ratio_threshold):
        self.window_length = window_length
        self.ratio_threshold = ratio_threshold
        self.sample_history = []


    def process(self, sample, mode="start"):
        self.sample_history.append(sample)
        if len(self.sample_history) < self.window_length * 2:
            return False
        if len(self.sample_history) > self.window_length * 2:
            self.sample_history.pop(0)

        window1 = self.sample_history[:self.window_length]
        window2 = self.sample_history[self.window_length:]
        energy1 = np.sum(np.abs(window1) ** 2)
        energy2 = np.sum(np.abs(window2) ** 2)

        if mode == "start":
            ratio = energy2 / energy1
            zero_crossings = np.sum(np.abs(np.diff(np.sign(window2)))) / 2
            # zero_crossings = 0
            if ratio > self.ratio_threshold and zero_crossings <= 1:
                return True
        elif mode == "end":
            ratio = energy1 / energy2
            if ratio > self.ratio_threshold:
                return True
        return False


    def reset(self):
        self.sample_history = []


class CostasLoop:
    def __init__(self, loop_bandwidth=0.063, damping_factor=0.707, phase=0, loop_filter_state=0):
        natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
        denominator = 4 + 4 * damping_factor * natural_freq + (natural_freq ** 2)
        self.C1 = 8 * damping_factor * natural_freq / denominator
        self.C2 = 4 * (natural_freq ** 2) / denominator

        self.phase = phase
        self.loop_filter_state = loop_filter_state


    def process(self, symbol, phase_detector=None):
        adjusted_symbol = symbol * np.exp(-1j * self.phase)
        if phase_detector == "monotone":
            phase_error = np.angle(adjusted_symbol)
        elif phase_detector == "bpsk":
            phase_error = np.angle(np.sign(np.real(adjusted_symbol)) * adjusted_symbol)
        elif phase_detector == "qam":
            phase_estimate = np.angle(QAM16().judge_symbol_as_symbol(adjusted_symbol))
            phase_error = np.angle(adjusted_symbol) - phase_estimate
            if phase_error > np.pi:
                phase_error -= 2 * np.pi
            elif phase_error < -np.pi:
                phase_error += 2 * np.pi
        else:
            phase_error = 0

        self.loop_filter_state += self.C2 * phase_error
        loop_filter_out = self.C1 * phase_error + self.loop_filter_state
        self.phase += loop_filter_out
        return adjusted_symbol, loop_filter_out


    def reset(self):
        self.phase = 0
        self.loop_filter_state = 0


class ClockSync:
    def __init__(self, K=0.5):
        self.sps = 4
        self.sample_count = -1
        self.sample_history = []

        self.K = K
        self.yi = 0
        self.yq = 0
        self.clock_offset = -1

        self.interpolation_method = "lagrange"
        self.farrow_matrix = np.array([
            [0, -1/3, 1/2, -1/6],
            [1, -1/2, -1, 1/2],
            [0, 1, 1/2, -1/2],
            [0, -1/6, 0, 1/6],
        ])


    def process(self, sample, detect=False):
        self.sample_history.append(sample)
        if len(self.sample_history) == self.sps + 1:
            self.sample_history.pop(0)
        self.sample_count += 1
        if self.sample_count == self.sps:
            self.sample_count = 0

        if detect and self.sample_count == self.sps - 1:
            s0 = np.abs(self.sample_history[0])
            s1 = np.abs(self.sample_history[1])
            s2 = np.abs(self.sample_history[2])
            s3 = np.abs(self.sample_history[3])
            self.yi = self.K * self.yi + s0 ** 2 - s2 ** 2
            self.yq = self.K * self.yq + s1 ** 2 - s3 ** 2
            clock_offset = np.arctan2(self.yq, self.yi) / 2 / np.pi
            if clock_offset < 0:
                clock_offset += 1
            self.clock_offset = clock_offset * self.sps

        if self.clock_offset != -1:
            symbol = None
            if self.interpolation_method == "nearest":
                if self.sample_count == self.sps - 1:
                    symbol = self.sample_history[round(self.clock_offset) % self.sps]

            elif self.interpolation_method == "linear":
                if int(self.clock_offset) != self.sps - 1 and self.sample_count == self.sps - 1:
                    delta_offset = self.clock_offset - int(self.clock_offset)
                    symbol = self.sample_history[int(self.clock_offset)] * (1 - delta_offset)
                    symbol += self.sample_history[int(self.clock_offset) + 1] * delta_offset
                elif int(self.clock_offset) == self.sps - 1 and self.sample_count == 0:
                    delta_offset = self.clock_offset - int(self.clock_offset)
                    symbol = self.sample_history[int(self.clock_offset) - 1] * (1 - delta_offset)
                    symbol += self.sample_history[int(self.clock_offset)] * delta_offset

            elif self.interpolation_method == "lagrange":
                filter_end = (int(self.clock_offset) + int(self.sps / 2)) % self.sps
                if self.sample_count == filter_end:
                    delta_offset = self.clock_offset - int(self.clock_offset)
                    mu_vector = np.array([1, delta_offset, delta_offset ** 2, delta_offset ** 3])
                    coefficients = np.dot(self.farrow_matrix, mu_vector)
                    symbol = np.dot(coefficients, self.sample_history)

            return symbol, self.clock_offset
        else:
            return None, None


    # def process2(self, samples, detect=False, interpolation_method="lagrange"):
    #     if len(samples) != 7:
    #         raise ValueError("ClockSync requires exactly 7 samples for processing.")
    #     if detect:
    #         s0 = np.real(samples[1])
    #         s1 = np.real(samples[2])
    #         s2 = np.real(samples[3])
    #         s3 = np.real(samples[4])
    #         self.yi = self.K * self.yi + s0 ** 2 - s2 ** 2
    #         self.yq = self.K * self.yq + s1 ** 2 - s3 ** 2
    #     clock_offset = np.arctan2(self.yq, self.yi) / 2 / np.pi
    #     if clock_offset < 0:
    #         clock_offset += 1
    #     clock_offset = clock_offset * 4

    #     floored_offset = int(clock_offset)
    #     delta_offset = clock_offset - floored_offset

    #     if interpolation_method == "linear":
    #         adjusted_symbol = samples[floored_offset + 1] * (1 - delta_offset) + samples[floored_offset + 2] * delta_offset
    #     elif interpolation_method == "lagrange":
    #         sample_vector = np.array(samples[floored_offset : floored_offset + 4])
    #         mu_vector = np.array([1, delta_offset, delta_offset ** 2, delta_offset ** 3])
    #         coefficients = np.dot(self.farrow_matrix, mu_vector)
    #         adjusted_symbol = np.dot(coefficients, sample_vector)
    #     return adjusted_symbol, clock_offset


    def reset(self):
        self.sample_count = -1
        self.sample_history = []

        self.yi = 0
        self.yq = 0
        self.clock_offset = -1


class AGC:
    def __init__(self):
        self.magnitude = []


    def process(self, symbol, detect=False):
        if detect:
            magnitude = np.abs(symbol)
            self.magnitude.append(magnitude)
        else:
            magnitude = None
        symbol = symbol / np.mean(self.magnitude) if self.magnitude else symbol
        return symbol, magnitude


    def reset(self):
        self.magnitude = []


class ChannelModel:
    def __init__(self, sample_rate, freq_offset=0, phase_offset=0, noise_std=0.01, attenuation=0):
        self.sample_rate = sample_rate
        self.freq_offset = freq_offset
        self.phase_offset = phase_offset
        self.noise_std = noise_std
        self.attenuation = attenuation


    def past(self, signal):
        t = np.arange(len(signal)) / self.sample_rate
        signal = signal * np.exp(1j * (2 * np.pi * self.freq_offset * t + self.phase_offset))
        signal += np.random.normal(0, self.noise_std, len(signal))
        signal += 1j * np.random.normal(0, self.noise_std, len(signal))
        signal *= 10 ** (-self.attenuation / 20)
        return signal


if __name__ == "__main__":
    bandwidth = 1e6
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    transmit_sample_rate = 1e9
    transmit_sps = transmit_sample_rate / symbol_rate
    over_sampling_ratio = 4 # receive sps
    receive_sample_rate = symbol_rate * over_sampling_ratio
    transceiver = BasebandTransceiver(
        transmit_sps=transmit_sps,
        receive_sps=over_sampling_ratio,
        rolloff_factor=rolloff_factor
    )

    data_transmit = np.random.randint(0, 2, size=1000)  # Random binary data
    transmitted_signal = transceiver.transmit(data_transmit)
    transmitted_signal = np.concatenate((np.zeros(123000), transmitted_signal, np.zeros(456000), transmitted_signal, np.zeros(7000)))
    # t = np.arange(len(transmitted_signal)) / transmit_sample_rate
    # utility.plot_signal(t, transmitted_signal, title="Transmitted Signal Waveform")
    # utility.plot_spectrum(transmitted_signal, fs=transmit_sample_rate, xlim=(-1.5e6, 1.5e6), title="Transmitted Signal Spectrum")

    channel = ChannelModel(sample_rate=transmit_sample_rate, freq_offset=1e5, phase_offset=0.3, noise_std=0.1, attenuation=20)
    received_signal = channel.past(transmitted_signal)
    # utility.plot_signal(t, received_signal, title="Received Signal Waveform")
    # utility.plot_spectrum(received_signal, fs=transmit_sample_rate, xlim=(-1.5e6, 1.5e6), title="Received Signal Spectrum")

    sampling_ratio = transmit_sample_rate / receive_sample_rate
    sample_offset = 0 # per symbol, from 0 to 1
    num_samples = int(len(received_signal) // sampling_ratio) - over_sampling_ratio
    sampled_signal = np.zeros(num_samples, dtype=complex)
    for i in range(num_samples):
        sampled_signal[i] = received_signal[round((i + (sample_offset * over_sampling_ratio)) * sampling_ratio)]
    utility.plot_sampled_signal(sampled_signal, title="Sampled Signal Waveform")
    utility.plot_spectrum(sampled_signal, fs=receive_sample_rate, xlim=(-1.5e6, 1.5e6), title="Sampled Signal Spectrum")

    sampled_signal1 = sampled_signal[:1000]
    sampled_signal2 = sampled_signal[1000:]

    data_received1, receiver_states, filtered_samples, sample_freq_offsets, freq_adjusted_samples, clock_offsets, magnitudes, symbols = transceiver.receive(sampled_signal1)
    data_received2, receiver_states, filtered_samples, sample_freq_offsets, freq_adjusted_samples, clock_offsets, magnitudes, symbols = transceiver.receive(sampled_signal2)
    data_received = data_received1 + data_received2
    utility.plot_sampled_signal(receiver_states, title="Receiver States")
    utility.plot_sampled_signal(filtered_samples, title="Filtered Samples")
    utility.plot_sampled_signal(np.array(sample_freq_offsets) * symbol_rate, title="Sample Frequency Offsets")
    utility.plot_sampled_signal(freq_adjusted_samples, title="Frequency Adjusted Samples")
    utility.plot_sampled_signal(clock_offsets, title="Clock Offsets")
    utility.plot_sampled_signal(magnitudes, title="AGC Magnitude")
    utility.plot_sampled_signal(symbols, title="Symbols")
    utility.plot_constellation(symbols, title="Symbols Constellation")

    if len(data_transmit) != len(data_received):
        print("Warning: Length of received message does not match transmitted data. Length:", len(data_received))
    error_count = np.sum(np.abs(data_received[:len(data_transmit)] - data_transmit[:len(data_received)]))
    print(f"Number of error bits: {error_count}")
    error_count2 = np.sum(np.abs(data_received[len(data_transmit):len(data_transmit)*2] - data_transmit[:len(data_received) - len(data_transmit)]))
    print(f"Number of error bits (second segment): {error_count2}")
