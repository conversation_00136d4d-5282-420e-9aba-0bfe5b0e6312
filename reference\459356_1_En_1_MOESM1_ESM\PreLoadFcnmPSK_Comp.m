% Preload Function of Model mPSK_Comp
% This function is executed before loading the model.
% It opens a figure window where the operator can enter
% the parameters of the simulation (using edit controls).
% When this is completed, the operator hits the Done button.
% This loads the content of the edit controls into workspace
% and saves the parameters to the parameter file params_BPSK_Comp.mat.

if exist('params_mPSK_Comp.mat')
    load params_mPSK_Comp; % load parameters from file
    disp(['Config file found', 10, 13]);
else
    % file not found, load default parameters
    fR = 100000;
    delta_f = 10000;
    nCycles = 20;
    fC = 400000;
    OS = 32;
    D = 8;
end

% Convert the parameters to strings. These will be entered into the
% edit controls.

fC_str = num2str(fC);
fR_str = num2str(fR);
nCycles_str = num2str(nCycles);
delta_f_str = num2str(delta_f);
OS_str = num2str(OS);
D_str = num2str(D);

% Create a figure object

flag = 0;
fig1 = figure('Units', 'normalized',...
              'Position', [0.1 0.1 0.6 0.5],...
              'Name', 'Parameters of Simulation');

% Create a static text window displaying operator instructions          
text1 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.05 0.8 0.25 0.08],...
                  'BackgroundColor', [1 0.5 1],...
                  'FontSize', 12);
% Wrap the text to be displayed on more than one line              
Msg_text1 = {'Enter/modify parameters of simulation'...
             'in edit controls.'...
             'Then hit ''Done'' button'};
[WrapString1, newpos1] = textwrap(text1, Msg_text1);
set(text1,'String',WrapString1,'Position',newpos1);

% create 6 static text windows + 5 edit controls
% static text names the parameters to be entered into edit controls
text2 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.9 0.3 0.04],...
                  'String', 'Carrier frequency [Hz]');
          
edit2 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.85 0.3 0.04],...
                  'Style', 'edit',...
                  'String', fC_str,...
                  'BackgroundColor', 'y');

text3 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.78 0.3 0.04],...
                  'String', 'Symbol rate [symb/s]');
          
edit3 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.73 0.3 0.04],...
                  'Style', 'edit',...
                  'String', fR_str,...
                  'BackgroundColor', 'y');

text4 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.66 0.3 0.04],...
                  'String', 'Oversampling factor (carrier)');
          
edit4 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.61 0.3 0.04],...
                  'Style', 'edit',...
                  'String', OS_str,...
                  'BackgroundColor', 'y');
              
text5 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.54 0.3 0.04],...
                  'String', 'Number of symbols');
          
edit5 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.49 0.3 0.04],...
                  'Style', 'edit',...
                  'String', nCycles_str,...
                  'BackgroundColor', 'y');              
              
text6 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.42 0.3 0.04],...
                  'String', 'Receiver offset freq [Hz]');
          
edit6 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.37 0.3 0.04],...
                  'Style', 'edit',...
                  'String', delta_f_str,...
                  'BackgroundColor', 'y');

text7 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.6 0.3 0.3 0.04],...
                  'String', 'Decimation factor');
          
edit7 = uicontrol('Parent', fig1,...
                  'Units', 'normalized',...
                  'Position', [0.6 0.25 0.3 0.04],...
                  'Style', 'edit',...
                  'String', D_str,...
                  'BackgroundColor', 'y');

% Create a pushbutton 'Done'
% This button must be hit at the end of parameter specification.
% Callback function tests validity of numeric entries.
% An error message is displayed in static text window text7 when
% the operator entered an invalid number.
% When ok, parameters are saved to file.
% The function also tests the OS entry. This must be an integer multiple
% of 4. If incorrect, the value is increased to the next higher integer
% multiple of 4.
but1 = uicontrol('Parent', fig1,...
                 'Units', 'normalized',...
                 'Position', [0.6 0.15 0.15 0.05],...
                 'Style', 'pushbutton',...
                 'String', 'Done',...
                 'Callback', ['fC_str = get(edit2, ''String'');'...
                              'fC = str2num(fC_str);'...
                              'fR_str = get(edit3, ''String'');'...
                              'fR = str2num(fR_str);'...
                              'OS_str = get(edit4, ''String'');'...
                              'OS = str2num(OS_str);'...
                              'nCycles_str = get(edit5, ''String'');'...
                              'nCycles = str2num(nCycles_str);'...
                              'delta_f_str = get(edit6, ''String'');'...
                              'delta_f = str2num(delta_f_str);'...
                              'D_str = get(edit7, ''String'');'...
                              'D = str2num(D_str);'...
                              'flag = 0;'...
                              'if isempty(fC) || isempty(fR) ||'...
                              '   isempty(OS) || isempty(nCycles) ||'...
                              '   isempty(delta_f) || isempty(D),'...
                              '         flag = 1;'...
                              'end;'...
                              'if flag == 1,'...
                              '   set(text8, ''Visible'', ''on'');'...
                              'else'...
                              '   set(text8, ''Visible'', ''off'');'...
                              '   fC = str2num(fC_str);'...
                              '   fR = str2num(fR_str);'...
                              '   OS = str2num(OS_str);'...
                              '   if rem(OS, 4) ~= 0,'...
                              '      OS = ceil(OS/4)*4;'...
                              '      set(edit4, ''String'', num2str(OS));'...
                              '   end;'...
                              '   nCycles = str2num(nCycles_str);'...
                              '   delta_f = str2num(delta_f_str);'...
                              '   D = str2num(D_str);'...
                              '   save params_mPSK_Comp fC fR OS nCycles delta_f D;'...
                              'end;']);

% Create a pushbutton that can be used to enter initial default values
% for all parameters.
but2 = uicontrol('Parent', fig1,...
                 'Units', 'normalized',...
                 'Position', [0.6 0.05 0.15 0.05],...
                 'Style', 'pushbutton',...
                 'String', 'Set init defaults',...
                 'Callback', ['fC = 400000;'...
                              'fR = 100000;'...
                              'OS = 32;'...
                              'nCycles = 20;'...
                              'delta_f = 10000;'...
                              'D = 8;'...
                              'set(edit2, ''String'', num2str(fC));'...
                              'set(edit3, ''String'', num2str(fR));'...
                              'set(edit4, ''String'', num2str(OS));'...
                              'set(edit5, ''String'', num2str(nCycles));'...
                              'set(edit6, ''String'', num2str(delta_f));'...
                              'set(edit7, ''String'', num2str(D));']);

text8 = uicontrol('Parent', fig1,...
                  'Style', 'Text',...
                  'Units', 'normalized',...
                  'Position', [0.05 0.3 0.25 0.08],...
                  'Visible', 'off',...
                  'BackgroundColor', 'r');
              
Msg_text8 = {'Error: invalid numeric format!'... 
             'Please correct entry(ies)'...
             'and hit ''Done'' button again.'};
[WrapString8, newpos8] = textwrap(text8, Msg_text8);
set(text8,'String',WrapString8,'Position',newpos8);


