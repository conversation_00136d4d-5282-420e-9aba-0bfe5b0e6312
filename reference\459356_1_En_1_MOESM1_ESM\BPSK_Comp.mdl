Model {
  Name			  "BPSK_Comp"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.84"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model BPSK_Comp\n----------------------\n\n1. Model description\n-----------------------\n\nThis is "
  "a model for BPSK communication systems. It simulates both the transmitter\nand the receiver. The receiver makes use "
  "of the \"modified Costas loop\" as \ndescribed in \"Communication system design using DSP algorithms\" by\nSteven A."
  " Tretter. \n\na. Transmitter\n----------------\nThe transmitter is built from the 5 blocks at the left of the block "
  "diagram.\nA sine wave block \"Carrier\" generates the carrier c(t). A random binary sequence\nis created by the bloc"
  "ks \"Random\" and \"Relay\". The \"Random\" block is sampled at\nfR, which is the symbol rate (in symbols/s). It gen"
  "erates a uniformly distributed \nsequence of numbers in the range -1 ... 1. The Relay block converts the sequence \n"
  "into a binary sequence having values 1 (corresponding to logical 1) and -1 (corresponding\nto logical 0), respective"
  "ly. The RTRand block is used for upsampling the binary\nsequence to the sampling frequency used for the carrier. The"
  " \"Modulation\" block\nbuilds the phase-modulated transmitter signal s(t). \n\nb. Receiver\n-------------\nBecause t"
  "he receiver is using a pre-envelope signal (analytic signal), we use a\nHilbert transformer to build the Hilbert tra"
  "nsform s^(t) of s(t). The Real-Imag to Complex\nblock \"R,I->Comp\" creates the complex pre-envelope signal s+(t). M"
  "ultiplication of s+(t)\nwith the reconstructed carrier exp(-j omegaC * t) yields the demodulated signal sdem:\n\n   "
  " sdem(t) = s+(t) exp(-j * omegaC * t)\n\nWhen the loop has correctly locked, sdem(t) equals the data signal m(t). Wh"
  "en the\nloop has notyet acquired lock, we need a phase error signal ud(t) that is used\nto control the frequency of "
  "the DCO (digital controlled oscillator). As theory shows\nthe signal ud can be built from the phase of the (complex)"
  " product sdem(t) * (Ib*)\n\n    ud = sdem(t) * (Ib*)\n\nwhere Ib the estimate of the data signal. Ib is obtained by "
  "the sign block sgn(I).\nBecause Ib is real, its complex conjugate Ib* is identical with Ib itself. The phase of\nthe"
  " product is obtained from the Complex to Magnitude/Phase block labelled C->M,phi.\n\nud is fed to the input of the l"
  "oop filter, given by block F3(z). F3(z) is a PI loop filter.\nIts output signal is given by uf(t). The DCO is built "
  "from the blocks Product4,\nK0, omega0, F4(z),  Constant, R,I->Compl, Mul2, and exp. The output of the\nexp block is "
  "the complex reconstructed carrier exp(-j  * omegaC * t). \n\n2. Parameters of the model\n---------------------------"
  "-----\nA number of parameters can be specified by the operator:\n\n- fC Carrier frequency of the transmitter (defaul"
  "t = 400000 Hz)\n- fR Symbol rate (default = 100000 symb/s)\n- OS Oversampling factor used in the transmitter section"
  ". The sampling frequency of\n      the model is defined as the product OS * fC. OS should be chosen to be an\n      "
  "integer multiple of 4 (default = 32). The reason for that is the following:\n      The Hilbert transformer Hilbert i"
  "s built from a simple delay circuit. It delays the\n      modulated carrier signal s(t) by OS/4 sampling intervals ("
  "default = 8 sampling\n      intervals). this corresponds to a quarter period of the carrier cosine wave.\n- nCycles "
  "Number of symbols used in the simulation (default = 20)\n- delta_f Fequency error of receiver carrier frequency. To "
  "simulate a frequency offset,\n      the initial frequency of the DCO is set to fC - delta_f.\n\n3. Instructions for "
  "model operation\n----------------------------------------\nTo perform simulations, proceed as follows:\n\n- Load the"
  " model BPSK_Comp by double-clicking the file BPSK_Comp.mdl in the\n  Current Folder of Matlab\n\n- This displays the"
  " model and a figure window containing  5 edit controls for\n  parameter specification. When the model runs the first"
  " time, default parameters are\n  set (cf. section 2). You now can alter these parameters. When done, click the 'Done"
  "'\n  button. When an invalid number format has been entered in one of the edit controls,\n  an error message is issu"
  "ed, telling you to correct this entry and to hit the 'Done'\n  button again. Hitting this button saves the actual pa"
  "rameters to a parameter file\n  params_BPSK_Comp.mat. When the model is started next, the parameters saved\n  in thi"
  "s files are loaded.\n  There is an option to load the initial default parameters: hit the 'Set init defaults' buttio"
  "n.\n  This can be useful whenever you specified parameters that don't give useful results\n\n- After hitting the 'Do"
  "ne' button, go to the model window and start the simulation\n  (menu item Simulation/Start).;\n\n- Look at the resul"
  "ts on the 3 scopes. \n  Here you can observe how the receiver acquires lock. Trace m(t) in scope\n  RF Signal shows "
  "the data signal created by the transmitter. Trace I in scope\n  I,Q shows the demodulated data signal which should b"
  "e identical with m(t).\n\n- From the waveforms in scope ph error the pull-in time can be estimated.\n  Increase e.g."
  " the frequency error delta_f, e.g. to 100000Hz and repeat\n  the simulation. Now you will see that the loop needs mo"
  "re time to lock.\n\n- When increasing the initial frequency error delta_f, you will recognize that\n  the pull-in ra"
  "nge of this type of Costas loop is much larger than the\n  pull-in range of the Costas loop with real signals (cf. m"
  "odel BFSK_Real)."
  SavedCharacterEncoding  "windows-1252"
  PreLoadFcn		  "PreLoadFcnBPSK_Comp"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  CloseFcn		  "CloseFcnBPSK_Comp"
  InitFcn		  "InitFcnBPSK_Comp"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Thu Dec 01 15:43:22 2016"
  RTWModifiedTimeStamp	  402507767
  ModelVersionFormat	  "1.%<AutoIncrement:84>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "7.81e-008"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "FixedStepDiscrete"
	  SolverName		  "FixedStepDiscrete"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  on
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "none"
	  UnconnectedOutputMsg	  "none"
	  UnconnectedLineMsg	  "none"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Diagnostics/Connectivity"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      ComplexToMagnitudeAngle
      Output		      "Magnitude and angle"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      ComplexToRealImag
      Output		      "Real and imag"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      InitialStates	      "0"
      SampleTime	      "1"
      a0EqualsOne	      off
      NumCoefMin	      "[]"
      NumCoefMax	      "[]"
      DenCoefMin	      "[]"
      DenCoefMax	      "[]"
      OutMin		      "[]"
      OutMax		      "[]"
      StateDataTypeStr	      "Inherit: Same as input"
      NumCoefDataTypeStr      "Inherit: Inherit via internal rule"
      DenCoefDataTypeStr      "Inherit: Inherit via internal rule"
      NumProductDataTypeStr   "Inherit: Inherit via internal rule"
      DenProductDataTypeStr   "Inherit: Inherit via internal rule"
      NumAccumDataTypeStr     "Inherit: Inherit via internal rule"
      DenAccumDataTypeStr     "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Ground
    }
    Block {
      BlockType		      Math
      Operator		      "exp"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      IntermediateResultsDataTypeStr "Inherit: Inherit via internal rule"
      AlgorithmType	      "Newton-Raphson"
      Iterations	      "3"
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RateTransition
      Integrity		      on
      Deterministic	      on
      X0		      "0"
      OutPortSampleTimeOpt    "Specify"
      OutPortSampleTimeMultiple	"1"
      OutPortSampleTime	      "-1"
    }
    Block {
      BlockType		      RealImagToComplex
      Input		      "Real and imag"
      ConstantPart	      "0"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "BPSK_Comp"
    Location		    [244, 258, 1233, 612]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "26"
    Block {
      BlockType		      ComplexToMagnitudeAngle
      Name		      "C->M,phi"
      SID		      "1"
      Ports		      [1, 2]
      Position		      [735, 13, 765, 42]
      Output		      "Magnitude and angle"
      Port {
	PortNumber		2
	Name			"ud"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "Carrier"
      SID		      "2"
      Ports		      [0, 1]
      Position		      [15, 95, 45, 125]
      Frequency		      "2513274.1229"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      ComplexToRealImag
      Name		      "Comp->R,I"
      SID		      "3"
      Ports		      [1, 2]
      Position		      [540, 123, 570, 152]
      Output		      "Real and imag"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
      Port {
	PortNumber		2
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "Constant"
      SID		      "4"
      Position		      [670, 295, 700, 325]
      BlockMirror	      on
      Value		      "-1"
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F3(z)"
      SID		      "5"
      Ports		      [1, 1]
      Position		      [820, 192, 880, 228]
      BlockMirror	      on
      Numerator		      "[102.8559     -100.8559]"
      Denominator	      "[512 -512]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"uf(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F4(z)"
      SID		      "6"
      Ports		      [1, 1]
      Position		      [640, 204, 685, 236]
      BlockMirror	      on
      Numerator		      "[0 7.8125e-008]"
      Denominator	      "[1 -1]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"phi2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Ground
      Name		      "Ground"
      SID		      "7"
      Position		      [665, 255, 685, 275]
      BlockMirror	      on
    }
    Block {
      BlockType		      Reference
      Name		      "Hilbert"
      SID		      "8"
      Ports		      [1, 1]
      Position		      [300, 143, 335, 177]
      LibraryVersion	      "1.225"
      UserDataPersistent      on
      UserData		      "DataTag0"
      SourceBlock	      "simulink/Discrete/Integer Delay"
      SourceType	      "Integer Delay"
      NumDelays		      "8"
      InputProcessing	      "Inherited"
      vinit		      "0.0"
      samptime		      "-1"
      Port {
	PortNumber		1
	Name			"s^(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "I,Q"
      SID		      "9"
      Ports		      [2]
      Position		      [935, 113, 965, 177]
      Floating		      off
      Location		      [18, 416, 431, 655]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "K0"
      SID		      "10"
      Position		      [820, 265, 850, 295]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "1263309.3633"
    }
    Block {
      BlockType		      Product
      Name		      "Modulation"
      SID		      "11"
      Ports		      [2, 1]
      Position		      [210, 102, 240, 133]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Mul1"
      SID		      "12"
      Ports		      [2, 1]
      Position		      [480, 122, 510, 153]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"sdem"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Mul2"
      SID		      "13"
      Ports		      [2, 1]
      Position		      [545, 212, 575, 243]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Mul4"
      SID		      "14"
      Ports		      [2, 1]
      Position		      [675, 12, 705, 43]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Product4"
      SID		      "15"
      Ports		      [2, 1]
      Position		      [755, 202, 785, 233]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->Comp"
      SID		      "16"
      Ports		      [2, 1]
      Position		      [405, 113, 435, 142]
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"s+(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->Comp1"
      SID		      "17"
      Ports		      [2, 1]
      Position		      [595, 288, 625, 317]
      BlockMirror	      on
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"-j"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "RF Signal"
      SID		      "18"
      Ports		      [3]
      Position		      [375, 196, 405, 264]
      Floating		      off
      Location		      [21, 114, 430, 353]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-1"
      YMax		      "1.1~1.1~1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTRand"
      SID		      "19"
      Position		      [125, 144, 165, 186]
      OutPortSampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"m(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "Random"
      SID		      "20"
      Position		      [15, 149, 45, 181]
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      Relay
      Name		      "Relay"
      SID		      "21"
      Position		      [70, 150, 100, 180]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "22"
      Ports		      [2, 1]
      Position		      [710, 210, 730, 230]
      BlockMirror	      on
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"omega2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Math
      Name		      "exp"
      SID		      "23"
      Ports		      [1, 1]
      Position		      [480, 215, 510, 245]
      BlockMirror	      on
      Port {
	PortNumber		1
	Name			"exp(-j phi2)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "omega0"
      SID		      "24"
      Position		      [750, 265, 780, 295]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "2450442.2698"
    }
    Block {
      BlockType		      Scope
      Name		      "ph error"
      SID		      "25"
      Ports		      [3]
      Position		      [935, 37, 965, 93]
      Floating		      off
      Location		      [17, 712, 432, 1018]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-0.6~2e+006~-1.6"
      YMax		      "0.6~2.7e+006~1.6"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Relay
      Name		      "sgn(I)"
      SID		      "26"
      Position		      [615, 55, 645, 85]
      OnSwitchValue	      "0"
      OffSwitchValue	      "0"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"Ib"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0]
      SrcBlock		      "Carrier"
      SrcPort		      1
      DstBlock		      "Modulation"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Relay"
      SrcPort		      1
      DstBlock		      "RTRand"
      DstPort		      1
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0]
      SrcBlock		      "Modulation"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[0, 40]
	Branch {
	  Points		  [0, 50]
	  DstBlock		  "RF Signal"
	  DstPort		  1
	}
	Branch {
	  DstBlock		  "Hilbert"
	  DstPort		  1
	}
      }
      Branch {
	DstBlock		"R,I->Comp"
	DstPort			1
      }
    }
    Line {
      Name		      "uf(t)"
      Labels		      [0, 0]
      SrcBlock		      "F3(z)"
      SrcPort		      1
      Points		      [-15, 0]
      Branch {
	DstBlock		"Product4"
	DstPort			1
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -165]
	DstBlock		"ph error"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "K0"
      SrcPort		      1
      Points		      [-15, 0]
      DstBlock		      "Product4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Product4"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "omega0"
      SrcPort		      1
      Points		      [-25, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "omega2"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	Labels			[2, 0]
	Points			[0, -155]
	DstBlock		"ph error"
	DstPort			2
      }
      Branch {
	DstBlock		"F4(z)"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Random"
      SrcPort		      1
      DstBlock		      "Relay"
      DstPort		      1
    }
    Line {
      Name		      "m(t)"
      Labels		      [0, 0]
      SrcBlock		      "RTRand"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	Points			[0, -40]
	DstBlock		"Modulation"
	DstPort			2
      }
      Branch {
	Labels			[1, 0]
	Points			[35, 0; 0, 65]
	DstBlock		"RF Signal"
	DstPort			2
      }
    }
    Line {
      Name		      "s^(t)"
      Labels		      [0, 0]
      SrcBlock		      "Hilbert"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[0, 90]
	DstBlock		"RF Signal"
	DstPort			3
      }
      Branch {
	Points			[0, -25]
	DstBlock		"R,I->Comp"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "Constant"
      SrcPort		      1
      DstBlock		      "R,I->Comp1"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Ground"
      SrcPort		      1
      Points		      [-15, 0; 0, 30]
      DstBlock		      "R,I->Comp1"
      DstPort		      1
    }
    Line {
      Name		      "phi2"
      Labels		      [0, 0]
      SrcBlock		      "F4(z)"
      SrcPort		      1
      DstBlock		      "Mul2"
      DstPort		      1
    }
    Line {
      Name		      "-j"
      Labels		      [0, 0]
      SrcBlock		      "R,I->Comp1"
      SrcPort		      1
      DstBlock		      "Mul2"
      DstPort		      2
    }
    Line {
      Name		      "s+(t)"
      Labels		      [0, 0]
      SrcBlock		      "R,I->Comp"
      SrcPort		      1
      DstBlock		      "Mul1"
      DstPort		      1
    }
    Line {
      Name		      "sdem"
      Labels		      [0, 0]
      SrcBlock		      "Mul1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Comp->R,I"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	Points			[0, -120]
	DstBlock		"Mul4"
	DstPort			1
      }
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "Comp->R,I"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	Points			[0, -60]
	DstBlock		"sgn(I)"
	DstPort			1
      }
      Branch {
	DstBlock		"I,Q"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "Comp->R,I"
      SrcPort		      2
      Points		      [0, 15]
      DstBlock		      "I,Q"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul2"
      SrcPort		      1
      DstBlock		      "exp"
      DstPort		      1
    }
    Line {
      Name		      "exp(-j phi2)"
      Labels		      [0, 0]
      SrcBlock		      "exp"
      SrcPort		      1
      Points		      [-20, 0; 0, -85]
      DstBlock		      "Mul1"
      DstPort		      2
    }
    Line {
      Name		      "Ib"
      Labels		      [0, 0]
      SrcBlock		      "sgn(I)"
      SrcPort		      1
      Points		      [10, 0]
      DstBlock		      "Mul4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul4"
      SrcPort		      1
      DstBlock		      "C->M,phi"
      DstPort		      1
    }
    Line {
      Name		      "ud"
      Labels		      [0, 0]
      SrcBlock		      "C->M,phi"
      SrcPort		      2
      Points		      [15, 0; 0, 50; 110, 0]
      Branch {
	DstBlock		"F3(z)"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	DstBlock		"ph error"
	DstPort			3
      }
    }
  }
}
MatData {
  NumRecords		  1
  DataRecord {
    Tag			    DataTag0
    Data		    "  %)30     .    B     8    (     @         %    \"     $    !     0         %  0 $P    $    3    :&%S26"
    "YH97)I=&5D3W!T:6]N        #@   #     &    \"     D\"        !0    @    !     0    $          @ !  $    "
  }
}
