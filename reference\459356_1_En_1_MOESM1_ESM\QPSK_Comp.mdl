Model {
  Name			  "QPSK_Comp"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.121"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model QPSK_Comp\n----------------------\n\n1. Model description\n-----------------------\nThis is a "
  "model for QPSK communication systems. It simulates both the transmitter\nand the receiver. It is similar to model QP"
  "SK_Real, but uses a \"modified\" Costas\nloop that operates with a complex input signal, i.e. with a pre-envelope si"
  "gnal.\n\n\na. Transmitter\n----------------\nThe transmitter is built from the 11 blocks at the left of the block di"
  "agram.\nA block \"CarrierI\" generates the cosine carrier c(t) = cos(omegaC  * t) where\nomegaC is the radian carrie"
  "r frequency. Another block CarrierQ generates the\nsine carrier sin(omegaC * t). Two binary random sequences m1 and "
  "m2 are created by\nblocks RandomI, SatC, RandomQ, and SatS. These random sequences build the\ndata signals I and Q, "
  "respectively. \nThe two rate transmission blocks RTI and RTQ are used to upsample the sampling\nfrequency of the ran"
  "dom sequences to the sampling frequency of the carrier \nsignals. \nThe transmitter output signal s(t) is then given"
  " by\n\n    s(t) = m1(t) * cos(omegaC * t) - m2(t) * sin(omegaC * t)\n\nNote: The choice of the - sign in the equatio"
  "n above is arbitrary. Both polarities\nare possible.\n\n\nb. Receiver\n-------------\nThe receiver is a \"modified\""
  " Costas loop for QPSK. It uses a complex input signal,\ni.e. the so-called pre-envelope signal s+(t). s+(t) is obtai"
  "ned from\n\n    s+(t) = s(t) + j * s^(t)\n\nHere s^ is the Hilbert transform of signal s(t), i.e. s^(t) = H[s(t)].\n"
  "In this model, the Hilbert transformer is\nbuilt from a simple phase shifter. It shifts the phase of s(t) by a quart"
  "er period of\nthe carrier frequency fC. Hence the cosine term is transformed into a sine term,\nand the sine term is"
  " transformed into a - cosine term. We then have\n\n    H[s(t)] = m1(t) * sin(omegaC * t) + m2(t) *  cos(omegaC * t)\n"
  "\nThe pre-envelope signal s+(t) now becomes\n\n    s+(t) = s(t) + j * s^(t) = m1(t) * cos(omegaC * t) - m2(t) * sin("
  "omegaC * t)\n                                        + j * sin(omegaC * t) + j * cos(omegaC * t)\n\nThis can be writ"
  "ten as\n\n    s+(t) = [m1(t) + j *m2(t)] * [cos(omegaC * t) + j* sin(omegaC * t)]\nor\n    s+(t) = [m1(t) + j*m2(t)]"
  " * exp(j * omegaC * t)\n\nThe term exp(j * omegaC * t) is interpreted as a complex carrier wave.\nIt is  very simple"
  " now to reconstruct the data signal. All we have to do is to\nmultiply s+(t) by another complex carrier of the form "
  "exp(- j * omegaC * t).\nThis yields\n\n    s~(t) = [m1(t) + j*m2(t)] * exp(j * omegaC * t) * exp(-j * omegaC * t)\no"
  "r\n    s~(t) = m1(t) + j*m2(t)\n\ns~(t) is called complex envelope of the data signal, i.e. the I signal is given by"
  "\nthe real part of s~(t), and the Q signal is given by the imaginary part of s~(t).\n\nWhen the loop has notyet acqu"
  "ired lock, there is, however, a phase error between\nthe carriers of transmitter and receiver. s~(t) is then represe"
  "nted by\n\n    s~(t) = [m1(t) + j*m2(t)] * exp(j * theta_e)\n\nwhere theta_e is the phase error. To extract the phas"
  "e error, we form an estimate\ne = Iest + j * Qest of the s~(t) signal. Iest is built from the sign of real part m1, "
  "and\nQest is built from the sign of the imaginary part m2:\n\n    Iest = sgn(m1), Qest = sgn(m2)\n\nand the estimate"
  " of the complex envelope becomes \n\n    Iest + j*Qest = sgn(m2) + j*sgn(m2)\n\nNow we build the complex conjugate o"
  "f the estimate: e* = Iest - j*Qest and multiply\nthe pre-envelope signal s~(t) with e*.\nThis finally yields:\n\n   "
  " s~(t) * e* = K* exp(j*theta_e)\n\nwhere K is a constant factor. The phase error theta_e is simply obtained by extra"
  "cting the\nphase of the product K* exp(j*theta_e); this yields\n\n    ud(t) = theta_e)\n\nLet us have now a look on "
  "the right part of the model block diagram. The blocks Hilbert and\nR,I->C3 create the pre-envelope signal s+(t). The"
  " model offers an option to\nscale down the sampling frequency of the receiver portion, i.e. rate transmission\nblock"
  " RTRot. Because the modified Costas loop does not create ac signals with\ntwice the frequency of carrier, the sampli"
  "ng rate can be much lower then the\nsampling rate required with the conventional Costas loop (cf. model \nQPSK_Real)"
  ".\n\nBlocks K0, omega0, F4(z), Mul4, and exp2 represent the DCO (digital controlled\noscillator). The output of bloc"
  "k exp2 is the complex carrier exp(- j*omega2*t),\nwith omega2 = instantaneous radian frequency fo the DCO. Multiplyi"
  "ng\nthat complex carrier with s+(t) yields the complex envelope s~(t). Blocks\nSgnI and SgnQ now build the estimate "
  "Iest, Qest of the complex envelope.\nMultiplication of s~(t) with the complex conjugate e* = Iest - j*Qest yields\n\n"
  "    s~(t) * e* = K exp(j*theta_e).\n\n Extracting the phase by block C->mag,phase  finally yields the phase error si"
  "gnal\n\n     ud(t) = theta_e\n\nThe loop filter is given by block F3(z). This is a PI filter.\n\n\n2. Parameters of "
  "the model\n--------------------------------\nA number of parameters can be specified by the operator:\n\n- fC Carrie"
  "r frequency of the transmitter (default = 400000 Hz)\n- fR Symbol rate (default = 100000 symb/s)\n- OS Oversampling "
  "factor used in the transmitter section. The sampling frequency of\n      the model is defined as the product OS * fC"
  ". (default = 32)\n- nCycles Number of symbols used in the simulation (default = 20)\n- delta_f Fequency error of rec"
  "eiver carrier frequency. To simulate a frequency offset,\n      the initial frequency of the DCO is set to fC - delt"
  "a_f.\n- D Decimation factor (default = 8). This allow to sample the blocks within the\n     receiver with a lower sa"
  "mpling frequency.\n\n\n3. Instructions for model operation\n----------------------------------------\nTo perform sim"
  "ulations, proceed as follows:\n\n- Load the model QPSK_Comp by double-clicking the file QPSK_Comp.mdl in the\n  Curr"
  "ent Folder of Matlab\n\n- This displays the model and a figure window containing  6 edit controls for\n  parameter s"
  "pecification. When the model runs the first time, default parameters are\n  set (cf. section 2). You now can alter t"
  "hese parameters. When done, click the 'Done'\n  button. When an invalid number format has been entered in one of the"
  " edit controls,\n  an error message is issued, telling you to correct this entry and to hit the 'Done'\n  button aga"
  "in. Hitting this button saves the actual parameters to a parameter file\n  params_QPSK_Comp.mat. When the model is s"
  "tarted next, the parameters saved\n  in this files are loaded.\n  There is an option to load the initial default par"
  "ameters: hit the 'Set init defaults' button.\n  This can be useful whenever you specified parameters that don't give"
  " useful results.\n\n- After hitting the 'Done' button, go to the model window and start the simulation\n  (menu item"
  " Simulation/Start).;\n\n- Look at the results on the scopes IQData, IQOut, and ph error . \n  Here you can observe h"
  "ow the receiver acquires lock. Traces I and Q in scope\n  IQData show the data signals created by the transmitter. T"
  "races I and Q in scope\n  IQOut show the demodulated data signals.\n\n- From the waveforms in scope ph error the pul"
  "l-in time can be estimated.\n  Increase e.g. the frequency error delta_f, e.g. to 50000Hz and repeat\n  the simulati"
  "on. Now you will see that the loop needs more time to lock.\n\n\n4. Comment on the pull-in range of the Costas loop\n"
  "-----------------------------------------------------------\nIn contrast to the conventional Costas loop (as used in"
  " model QPSK_Real), the\nmodified Costas loop does not require an additional lowpass filter to remove the\nunwanted d"
  "ouble-frequency components. Hence there is no additional phase shift\nin the Costas loop, and the pull-in range can "
  "be arbitrarily high.\nWhen selecting a higher delta_f value, it will be necessary to increase the duration\nof the s"
  "imulation (increase nCycles), because the pull-in time TP will become larger.\nWhen very large frequency errors are "
  "simulated, you will note that the loop is no\nlonger able to acquire lock when larger decimation factors are used. T"
  "his stems from the\nfact that the output signal of block Mul3 will contain very high frequency components,\ni.e. wav"
  "eforms whose frequency is up to 4 times the frequency error. To process those\nhigh-frequency signals a large sampli"
  "ng rate must be selected, otherwise we will be\nconfronted with aliasing effects.\n\n\n5. Comment on the \"phase amb"
  "iguity\" of the Costas loop\n-----------------------------------------------------------------\nWhen performing simu"
  "lations with different values of frequency error\ndelta_f you will recognize in some situations that the polarity of"
  " the received signals\ncan be inverted, or the I signal can be exchanged with the Q signal.\nThis occurs because the"
  " Costas looop for BQPSK can lock with 4 possible \nphase differences between transmitter and receiver carriers, i. e"
  ". with a \nphase difference of 0, 90, 180, or 270 degrees.\nTo avoid this ambiguity additional measures have to been"
  " taken. A common\nmethod is to use a given preamble at each start of a data transmission,\ne.g. a sequence of all 1'"
  "s or all 0's or another predifined pattern.\nBecause the receiver \"knows\" that preamble it will use the preamble \n"
  "pattern in place of the demodulated I signal at start of every data \ntransmission. This method is demonstrated in a"
  "n other model (BPSK_Real_PreAmb).\nThe same procedure could be applied in this model, i.e. we would have to \ncreate"
  " a predefined preamble for both I and Q signals, e.g. a sequence of all 1's.\n\n\n"
  SavedCharacterEncoding  "windows-1252"
  PreLoadFcn		  "PreLoadFcnQPSK_Comp"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  CloseFcn		  "CloseFcnQPSK_Comp"
  InitFcn		  "InitFcnQPSK_Comp"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Sat Apr 27 16:24:28 2013"
  RTWModifiedTimeStamp	  288979735
  ModelVersionFormat	  "1.%<AutoIncrement:121>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "7.81e-008"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "FixedStepDiscrete"
	  SolverName		  "FixedStepDiscrete"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  on
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "none"
	  UnconnectedOutputMsg	  "none"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Diagnostics/Connectivity"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      ComplexToMagnitudeAngle
      Output		      "Magnitude and angle"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      ComplexToRealImag
      Output		      "Real and imag"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      InitialStates	      "0"
      SampleTime	      "1"
      a0EqualsOne	      off
      NumCoefMin	      "[]"
      NumCoefMax	      "[]"
      DenCoefMin	      "[]"
      DenCoefMax	      "[]"
      OutMin		      "[]"
      OutMax		      "[]"
      StateDataTypeStr	      "Inherit: Same as input"
      NumCoefDataTypeStr      "Inherit: Inherit via internal rule"
      DenCoefDataTypeStr      "Inherit: Inherit via internal rule"
      NumProductDataTypeStr   "Inherit: Inherit via internal rule"
      DenProductDataTypeStr   "Inherit: Inherit via internal rule"
      NumAccumDataTypeStr     "Inherit: Inherit via internal rule"
      DenAccumDataTypeStr     "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParamMin		      "[]"
      ParamMax		      "[]"
      ParamDataTypeStr	      "Inherit: Same as input"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Ground
    }
    Block {
      BlockType		      Math
      Operator		      "exp"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      IntermediateResultsDataTypeStr "Inherit: Inherit via internal rule"
      AlgorithmType	      "Newton-Raphson"
      Iterations	      "3"
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RateTransition
      Integrity		      on
      Deterministic	      on
      X0		      "0"
      OutPortSampleTimeOpt    "Specify"
      OutPortSampleTimeMultiple	"1"
      OutPortSampleTime	      "-1"
    }
    Block {
      BlockType		      RealImagToComplex
      Input		      "Real and imag"
      ConstantPart	      "0"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Signum
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "QPSK_Comp"
    Location		    [91, 242, 1247, 750]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "101"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "50"
    Block {
      BlockType		      ComplexToRealImag
      Name		      "C->R,I2"
      SID		      "2"
      Ports		      [1, 2]
      Position		      [665, 195, 695, 235]
      Output		      "Real and imag"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
      Port {
	PortNumber		2
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      ComplexToMagnitudeAngle
      Name		      "C->mag,phase"
      SID		      "50"
      Ports		      [1, 2]
      Position		      [985, 193, 1015, 222]
      Output		      "Magnitude and angle"
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierI"
      SID		      "43"
      Ports		      [0, 1]
      Position		      [70, 40, 100, 70]
      Frequency		      "2513274.1229"
      Phase		      "pi/2"
      SampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierQ"
      SID		      "44"
      Ports		      [0, 1]
      Position		      [70, 285, 100, 315]
      Frequency		      "2513274.1229"
      SampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F3(z)"
      SID		      "5"
      Ports		      [1, 1]
      Position		      [970, 317, 1030, 353]
      BlockMirror	      on
      Numerator		      "[102.8885     -100.8885]"
      Denominator	      "[512.1639     -512.1639]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"uf(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F4(z)"
      SID		      "6"
      Ports		      [1, 1]
      Position		      [715, 326, 765, 364]
      BlockMirror	      on
      Numerator		      "[0   7.81e-008]"
      Denominator	      "[1 -1]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"phi2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Ground
      Name		      "GND"
      SID		      "7"
      Position		      [515, 410, 535, 430]
    }
    Block {
      BlockType		      Gain
      Name		      "Gain"
      SID		      "8"
      Position		      [660, 435, 690, 465]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"-j"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Gain1"
      SID		      "9"
      Position		      [800, 225, 830, 255]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Reference
      Name		      "Hilbert"
      SID		      "10"
      Ports		      [1, 1]
      Position		      [370, 223, 405, 257]
      LibraryVersion	      "1.225"
      UserDataPersistent      on
      UserData		      "DataTag0"
      SourceBlock	      "simulink/Discrete/Integer Delay"
      SourceType	      "Integer Delay"
      NumDelays		      "8"
      InputProcessing	      "Inherited"
      vinit		      "0.0"
      samptime		      "-1"
      Port {
	PortNumber		1
	Name			"s^(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "IQData"
      SID		      "48"
      Ports		      [3]
      Position		      [375, 53, 405, 137]
      Floating		      off
      Location		      [70, 428, 700, 670]
      Open		      on
      NumInputPorts	      "3"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-1.5"
      YMax		      "1.1~1.1~1.5"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "IQOut"
      SID		      "12"
      Ports		      [2]
      Position		      [860, 66, 890, 99]
      Floating		      off
      Location		      [70, 710, 700, 949]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "yonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.5~-1.5"
      YMax		      "1.5~1.5"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "K0"
      SID		      "14"
      Position		      [955, 390, 985, 420]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "1263309.3633"
    }
    Block {
      BlockType		      Product
      Name		      "Mul3"
      SID		      "17"
      Ports		      [2, 1]
      Position		      [600, 197, 630, 228]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s~(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Mul4"
      SID		      "18"
      Ports		      [2, 1]
      Position		      [650, 337, 680, 368]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Mul5"
      SID		      "19"
      Ports		      [2, 1]
      Position		      [875, 327, 905, 358]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Mul6"
      SID		      "20"
      Ports		      [2, 1]
      Position		      [925, 192, 955, 223]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI"
      SID		      "45"
      Ports		      [2, 1]
      Position		      [220, 47, 250, 78]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ"
      SID		      "46"
      Ports		      [2, 1]
      Position		      [210, 277, 240, 308]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->C2"
      SID		      "22"
      Ports		      [2, 1]
      Position		      [585, 433, 615, 462]
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"j"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->C3"
      SID		      "23"
      Ports		      [2, 1]
      Position		      [455, 188, 485, 217]
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"s+(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->C4"
      SID		      "24"
      Ports		      [2, 1]
      Position		      [850, 198, 880, 227]
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"e*"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTI"
      SID		      "27"
      Position		      [130, 119, 170, 161]
      OutPortSampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"I"
	PropagatedSignals	"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTQ"
      SID		      "42"
      Position		      [130, 194, 170, 236]
      OutPortSampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"Q"
	PropagatedSignals	"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTRot"
      SID		      "26"
      Position		      [530, 184, 570, 226]
      OutPortSampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomI"
      SID		      "28"
      Position		      [15, 124, 45, 156]
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomQ"
      SID		      "29"
      Position		      [15, 199, 45, 231]
      Seed		      "1"
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      Relay
      Name		      "SatC"
      SID		      "30"
      Position		      [70, 125, 100, 155]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "SatS"
      SID		      "31"
      Position		      [70, 200, 100, 230]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Signum
      Name		      "SgnI"
      SID		      "32"
      Position		      [740, 165, 770, 195]
      Port {
	PortNumber		1
	Name			"Iest"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Signum
      Name		      "SgnQ"
      SID		      "33"
      Position		      [740, 225, 770, 255]
      Port {
	PortNumber		1
	Name			"Qest"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "34"
      Ports		      [2, 1]
      Position		      [825, 335, 845, 355]
      BlockMirror	      on
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"omega2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum1"
      SID		      "47"
      Ports		      [2, 1]
      Position		      [295, 165, 320, 220]
      ShowName		      off
      Inputs		      "+-"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Math
      Name		      "exp2"
      SID		      "37"
      Ports		      [1, 1]
      Position		      [595, 340, 625, 370]
      BlockMirror	      on
      Port {
	PortNumber		1
	Name			"exp(-j phi2)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "imag"
      SID		      "38"
      Position		      [510, 450, 540, 480]
    }
    Block {
      BlockType		      Constant
      Name		      "omega0"
      SID		      "39"
      Position		      [860, 390, 890, 420]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "2450442.2698"
    }
    Block {
      BlockType		      Scope
      Name		      "ph error"
      SID		      "41"
      Ports		      [3]
      Position		      [1090, 248, 1120, 282]
      Floating		      off
      Location		      [72, 107, 702, 398]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "yonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-2~-1~1000000"
      YMax		      "2~1~3000000"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Line {
      Name		      "uf(t)"
      Labels		      [0, 0]
      SrcBlock		      "F3(z)"
      SrcPort		      1
      Points		      [-25, 0]
      Branch {
	DstBlock		"Mul5"
	DstPort			1
      }
      Branch {
	Points			[0, -70]
	DstBlock		"ph error"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "K0"
      SrcPort		      1
      Points		      [-30, 0]
      DstBlock		      "Mul5"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul5"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "omega0"
      SrcPort		      1
      Points		      [-20, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "omega2"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [-10, 0]
      Branch {
	Points			[0, -70]
	DstBlock		"ph error"
	DstPort			3
      }
      Branch {
	DstBlock		"F4(z)"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "RandomI"
      SrcPort		      1
      DstBlock		      "SatC"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RandomQ"
      SrcPort		      1
      DstBlock		      "SatS"
      DstPort		      1
    }
    Line {
      SrcBlock		      "GND"
      SrcPort		      1
      Points		      [15, 0; 0, 20]
      DstBlock		      "R,I->C2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "imag"
      SrcPort		      1
      Points		      [10, 0; 0, -10]
      DstBlock		      "R,I->C2"
      DstPort		      2
    }
    Line {
      Name		      "j"
      Labels		      [0, 0]
      SrcBlock		      "R,I->C2"
      SrcPort		      1
      DstBlock		      "Gain"
      DstPort		      1
    }
    Line {
      Name		      "s^(t)"
      Labels		      [0, 0]
      SrcBlock		      "Hilbert"
      SrcPort		      1
      Points		      [15, 0; 0, -30]
      DstBlock		      "R,I->C3"
      DstPort		      2
    }
    Line {
      Name		      "s+(t)"
      Labels		      [0, 0]
      SrcBlock		      "R,I->C3"
      SrcPort		      1
      DstBlock		      "RTRot"
      DstPort		      1
    }
    Line {
      Name		      "phi2"
      Labels		      [0, 0]
      SrcBlock		      "F4(z)"
      SrcPort		      1
      DstBlock		      "Mul4"
      DstPort		      1
    }
    Line {
      Name		      "exp(-j phi2)"
      Labels		      [0, 1]
      SrcBlock		      "exp2"
      SrcPort		      1
      Points		      [-10, 0; 0, -135]
      DstBlock		      "Mul3"
      DstPort		      2
    }
    Line {
      Name		      "s~(t)"
      Labels		      [0, 0]
      SrcBlock		      "Mul3"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"C->R,I2"
	DstPort			1
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -65; 265, 0; 0, 50]
	DstBlock		"Mul6"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Mul4"
      SrcPort		      1
      DstBlock		      "exp2"
      DstPort		      1
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "C->R,I2"
      SrcPort		      1
      Points		      [5, 0; 0, -25]
      Branch {
	DstBlock		"SgnI"
	DstPort			1
      }
      Branch {
	Points			[0, -105]
	DstBlock		"IQOut"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "C->R,I2"
      SrcPort		      2
      Points		      [5, 0; 0, 15; 15, 0]
      Branch {
	DstBlock		"SgnQ"
	DstPort			1
      }
      Branch {
	Points			[0, -150]
	DstBlock		"IQOut"
	DstPort			2
      }
    }
    Line {
      Name		      "Qest"
      Labels		      [0, 0]
      SrcBlock		      "SgnQ"
      SrcPort		      1
      DstBlock		      "Gain1"
      DstPort		      1
    }
    Line {
      Name		      "Iest"
      Labels		      [0, 0]
      SrcBlock		      "SgnI"
      SrcPort		      1
      Points		      [15, 0; 0, 25]
      DstBlock		      "R,I->C4"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Gain1"
      SrcPort		      1
      DstBlock		      "R,I->C4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul6"
      SrcPort		      1
      DstBlock		      "C->mag,phase"
      DstPort		      1
    }
    Line {
      Name		      "e*"
      Labels		      [0, 0]
      SrcBlock		      "R,I->C4"
      SrcPort		      1
      DstBlock		      "Mul6"
      DstPort		      2
    }
    Line {
      SrcBlock		      "RTRot"
      SrcPort		      1
      DstBlock		      "Mul3"
      DstPort		      1
    }
    Line {
      Name		      "I"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "SatC"
      SrcPort		      1
      DstBlock		      "RTI"
      DstPort		      1
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "SatS"
      SrcPort		      1
      DstBlock		      "RTQ"
      DstPort		      1
    }
    Line {
      SrcBlock		      "CarrierI"
      SrcPort		      1
      DstBlock		      "MulI"
      DstPort		      1
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "RTI"
      SrcPort		      1
      Points		      [15, 0; 0, 0]
      Branch {
	Points			[0, -70]
	DstBlock		"MulI"
	DstPort			2
      }
      Branch {
	Points			[90, 0; 0, -75]
	DstBlock		"IQData"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "CarrierQ"
      SrcPort		      1
      DstBlock		      "MulQ"
      DstPort		      2
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "RTQ"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[0, 70]
	DstBlock		"MulQ"
	DstPort			1
      }
      Branch {
	Points			[0, -60; 115, 0; 0, -60]
	DstBlock		"IQData"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "MulI"
      SrcPort		      1
      Points		      [10, 0; 0, 115]
      DstBlock		      "Sum1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulQ"
      SrcPort		      1
      Points		      [20, 0; 0, -90]
      DstBlock		      "Sum1"
      DstPort		      2
    }
    Line {
      Name		      "s(t)"
      SrcBlock		      "Sum1"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	Labels			[1, 0]
	DstBlock		"R,I->C3"
	DstPort			1
      }
      Branch {
	Points			[0, 45]
	DstBlock		"Hilbert"
	DstPort			1
      }
      Branch {
	Points			[0, -70]
	DstBlock		"IQData"
	DstPort			3
      }
    }
    Line {
      Name		      "-j"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "Gain"
      SrcPort		      1
      Points		      [5, 0; 0, -90]
      DstBlock		      "Mul4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "C->mag,phase"
      SrcPort		      2
      Points		      [35, 0; 0, 40]
      Branch {
	Points			[0, 80]
	DstBlock		"F3(z)"
	DstPort			1
      }
      Branch {
	DstBlock		"ph error"
	DstPort			1
      }
    }
    Annotation {
      Name		      "Local carrier\nexp(-j*phi2)"
      Position		      [606, 396]
    }
    Annotation {
      Name		      "DCO is built from integrator\nF4(z) and surrounding blocks"
      Position		      [796, 447]
    }
    Annotation {
      Name		      "Loop filter F3(z)"
      Position		      [1041, 376]
    }
    Annotation {
      Name		      "Iest,Qest = estimate\n of received symbol I, Q"
      Position		      [726, 284]
    }
    Annotation {
      Name		      "e = Iest + j*Qest\ne is estimate of complex\nenvelope s~(t)\ne* is conjugate of estimate"
      Position		      [998, 118]
    }
    Annotation {
      Name		      "Modified  4-phase Costas loop"
      Position		      [498, 31]
    }
    Annotation {
      Name		      "Rotator"
      Position		      [605, 182]
    }
    Annotation {
      Name		      "Transmitter"
      Position		      [118, 27]
    }
    Annotation {
      Name		      "Receiver"
      Position		      [674, 30]
    }
    Annotation {
      Name		      "s+(t) = pre-envelope signal"
      Position		      [464, 164]
    }
    Annotation {
      Name		      "s~(t) is complex envelope of data signal"
      Position		      [680, 125]
    }
    Annotation {
      Name		      "ud(t)"
      Position		      [1045, 211]
    }
  }
}
MatData {
  NumRecords		  1
  DataRecord {
    Tag			    DataTag0
    Data		    "  %)30     .    B     8    (     @         %    \"     $    !     0         %  0 $P    $    3    :&%S26"
    "YH97)I=&5D3W!T:6]N        #@   #     &    \"     D\"        !0    @    !     0    $          @ !  $    "
  }
}
