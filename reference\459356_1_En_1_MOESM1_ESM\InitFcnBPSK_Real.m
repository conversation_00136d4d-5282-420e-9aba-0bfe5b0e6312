% Init Function of Model BPSK_Real
% This function is loaded whenever a simulation is started
% It loads the parameters of the simulation (as specified in the preceding
% PreLoadFcnBPSK_Real.m) onto the model


% Calculate the parameters of the model

fS = OS * fC;  % Define the sampling frequency of the (discrete) model
T = 1/fS;      % sampling interal of the model
f3dB = 2 * fR;
omega3dB = 2 * pi * f3dB;
fT = 0.4 * fR; % Transit frequency of PLL (Costas loop)
omegaT = 2 * pi * fT;
tau1 = 20e-6;  % integrator time constant in PI loop filter 
K0 = tau1 * omegaT^2; % VCO gain
omega0 = 2 * pi * (fC - delta_f);
tR = 1/fR; % duration of one symbol (bit)
omegaC = 2 * pi * fC;

% Check the ratio of sampling times of the model. This ratio must be
% an integer number. If not, round sampling times to fulfill that
% requirement.

T_string = num2str(T, 3);
% Note: T_string is string variable of sampling time T (rounded)
T_round = str2num(T_string);
tR_string = num2str(tR, 3);
tR_round = str2num(tR_string);
K = round(tR_round/T_round); % round ratio of sampling times to integer
tR_string = num2str(K*T_round, 9);
% Note: tR_string is string variable of sampling time tR (rounded)

% prewarp corner frequencies (for bilinear z transform)
omega3dB_P = (2/T) * tan(omega3dB * T/2);
omegaT_P = (2/T) * tan(omegaT * T/2);

% enter parameters into the model

set_param('BPSK_Real', 'FixedStep', T_string); % sampling time of model
set_param('BPSK_Real', 'Stop time', num2str(nCycles * tR)); 
set_param('BPSK_Real/RTRand', 'OutPortSampleTime', T_string);

set_param('BPSK_Real/Carrier', 'Sample time', T_string);
set_param('BPSK_Real/Carrier', 'Frequency', num2str(omegaC));

set_param('BPSK_Real/Random', 'Sample time', tR_string);

set_param('BPSK_Real/F1(z)', 'Numerator', ['[', num2str([1, 1]), ']']); 
set_param('BPSK_Real/F1(z)', 'Denominator', ['[', num2str([1+2/(omega3dB_P*T), 1-2/(omega3dB_P*T)]), ']']); 
set_param('BPSK_Real/F1(z)', 'Sample time', T_string);

set_param('BPSK_Real/F2(z)', 'Numerator', ['[', num2str([1, 1]), ']']); 
set_param('BPSK_Real/F2(z)', 'Denominator', ['[', num2str([1+2/(omega3dB_P*T), 1-2/(omega3dB_P*T)]), ']']);
set_param('BPSK_Real/F2(z)', 'Sample time', T_string);

set_param('BPSK_Real/F3(z)', 'Numerator', ['[', num2str([1+2/(omegaT_P*T), 1-2/(omegaT_P*T)]), ']']); 
set_param('BPSK_Real/F3(z)', 'Denominator', ['[', num2str([2*tau1/T, -2*tau1/T]), ']']);
set_param('BPSK_Real/F3(z)', 'Sample time', T_string);

set_param('BPSK_Real/F4(z)', 'Numerator', ['[', num2str([0, T]), ']']);
set_param('BPSK_Real/F4(z)', 'Denominator', ['[', num2str([1, -1]), ']']);
set_param('BPSK_Real/F4(z)', 'Sample time', T_string);

set_param('BPSK_Real/K0', 'Value', num2str(K0));

set_param('BPSK_Real/omega0', 'Value', num2str(omega0));

