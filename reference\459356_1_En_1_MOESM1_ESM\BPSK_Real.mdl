Model {
  Name			  "BPSK_Real"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.80"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model BPSK_Real\n----------------------\n\n1. Model description\n-----------------------\n\nThis is "
  "a model for BPSK communication systems. It simulates both the transmitter\nand the receiver. The receiver uses a con"
  "ventional Costas loop for carrier\nrecovery. This is in contrast to model BPSK_Comp where a \"modified\"\nCostas loo"
  "p is applied (cf. Description in model BPSK_Comp). \n\n\na. Transmitter\n----------------\nThe transmitter is built "
  "from the 5 blocks at the left of the block diagram.\nA sine wave block \"Carrier\" generates the carrier c(t). A ran"
  "dom binary sequence\nis created by the blocks \"Random\" and \"Relay\". The \"Random\" block is sampled at\nfR, whic"
  "h is the symbol rate (in symbols/s). It generates a uniformly distributed \nsequence of numbers in the range -1 ... "
  "1. The Relay block converts the sequence \ninto a binary sequence having values 1 (corresponding to logical 1) and -"
  "1 (corresponding\nto logical 0), respectively. The RTRand block is used for upsampling the binary\nsequence to the s"
  "ampling frequency used for the carrier. The \"Modulation\" blocks\nbuilds the phase-modulated transmitter signal c(t"
  ")m(t). \n\nb. Receiver\n-------------\nThe receiver is a conventional Costas loop for BPSK. The reconstructed carrie"
  "r is a\nquadrature signal built from a sine and a cosine component (cf. output of blocks\nProduct1 nand Product2). T"
  "he received signal c(t)m(t) is multiplied by both the\nsine (LOsin) and the cosine (LOcos) component by blocks Mixer"
  "I and MixerQ, respectively.\nThe output signal of these multipliers form the so called I and Q paths. When the loop\n"
  "has acquired lock, the output of block MixerI is the demodulated data signal (I), but\nalso contains an ac term at t"
  "wice the carrier frequency. Lowpass filters F1(z) and F2(z)\nare used to remove that ac term. \nThe phase error sign"
  "al ud that is used to control the frequency of the DCO (digital \ncontrolled oscillator) is obtained by muliplying t"
  "he I with the Q signal. Theory shows\nthat for small phase error ud is proportional to 2 theta_e, where theta_e\nis "
  "the phase error. The ud signal is applied to the loop filter built from block F3(z). \nThis filter is built as a PI "
  "block (proportional + integral). \nThe output signal uf of the loop filter controls the frequency omega2 of the DCO."
  "\nThe DCO is built from blocks Product4, K0, omega0, F4(z), cos, and sin. \n\n\n2. Parameters of the model\n--------"
  "------------------------\nA number of parameters can be specified by the operator:\n\n- fC Carrier frequency of the "
  "transmitter (default = 400000 Hz)\n- fR Symbol rate (default = 100000 symb/s)\n- OS Oversampling factor used in the "
  "transmitter section. The sampling frequency of\n      the model is defined as the product OS * fC. (default = 32)\n-"
  " nCycles Number of symbols used in the simulation (default = 20)\n- delta_f Fequency error of receiver carrier frequ"
  "ency. To simulate a frequency offset,\n      the initial frequency of the DCO is set to fC - delta_f.\n\n\n3. Instru"
  "ctions for model operation\n----------------------------------------\nTo perform simulations, proceed as follows:\n\n"
  "- Load the model BPSK_Real by double-clicking the file BPSK_Real.mdl in the\n  Current Folder of Matlab\n\n- This di"
  "splays the model and a figure window containing  5 edit controls for\n  parameter specification. When the model runs"
  " the first time, default parameters are\n  set (cf. section 2). You now can alter these parameters. When done, click"
  " the 'Done'\n  button. When an invalid number format has been entered in one of the edit controls,\n  an error messa"
  "ge is issued, telling you to correct this entry and to hit the 'Done'\n  button again. Hitting this button saves the"
  " actual parameters to a parameter file\n  params_BPSK_Real.mat. When the model is started next, the parameters saved"
  "\n  in this files are loaded.\n  There is an option to load the initial default parameters: hit the 'Set init defaul"
  "ts' buttion.\n  This can be useful whenever you specified parameters that don't give useful results\n\n- After hitti"
  "ng the 'Done' button, go to the model window and start the simulation\n  (menu item Simulation/Start).;\n\n- Look at"
  " the results on the scopes RF Signal, I,Q, and ph error . \n  Here you can observe how the receiver acquires lock. T"
  "race m(t) in scope\n  RF Signal shows the data signal created by the transmitter. Trace I in scope\n  I,Q shows the "
  "demodulated data signal which should be identical with m(t).\n\n- From the waveforms in scope ph error the pull-in t"
  "ime can be estimated.\n  Increase e.g. the frequency error delta_f, e.g. to 100000Hz and repeat\n  the simulation. N"
  "ow you will see that the loop needs more time to lock.\n\n\n4. Comment on the pull-in range of the Costas loop\n----"
  "-------------------------------------------------------\nA conventional PLL that uses a loop filter with an integrat"
  "or (PI filter) is\nexpected to show an \"infinite\" pull-in range delta_f_p. In practice, the pull-in\nrange is limi"
  "ted by the frequency range the DCO (or VCO) is able to\ngenerate. The situation is different, however,  with this ty"
  "pe  of Costas\nloop. When lock has notyet been acquired, theory shows that the error\nsignal ud created by the phase"
  " detector contains a dc component that\nis inversely proportional to the frequency error (difference between \ntrans"
  "mitter and receiver carrier frequencies). But theory says furthermore\nthat this dc term is multiplied by a cosine t"
  "erm cos(2*ph1 + phi3), where\nphi1 is the phase shift of the lowpass filter F1(z) and phi3 is the phase\nshift of th"
  "e loop filter F3(z). (The phase shift of F2(z) is identical with the\nphase shift of F1(z). When the frequency error"
  " is large, the sum 2*phi1\n+ phi3 exceeds 90 degrees, hence the cosine term becomes negative.\nThis implies that the"
  " dc component of the ud signal changes polarity, and\nthe frequency control of the loop operates in the \"wrong\" di"
  "rection.\nSimulations with different values of parameter delta_f show that the\npull-in range of this Costas loop is"
  " around 110 kHz. (Theory predicts a \npull-in range of approximately 130 kHz, which is not very far away from\nthe s"
  "imulation result.)\n\n\n5. Comment on the \"phase ambiguity\" of the Costas loop\n----------------------------------"
  "-------------------------------\nWhen performing simulations with different values of frequency error\ndelta_f you w"
  "ill recognize that the loop may lock with \"inverted\" polarity,\ni.e. the recovered data signal I is in antiphase w"
  "ith the modulating \nsignal m(t). This occurs because the Costas looop for BPSK can lock\nwith a phase difference of"
  " 0 or 180 degrees. \nTo avoid the ambiguity additional measures have to been taken. A common\nmethod is to use a giv"
  "en preamble at each start of a data transmission,\ne.g. a sequence of all 1's or all 0's or another predifined patte"
  "rn.\nBecause the receiver \"knows\" that preamble it will use the preamble \npattern in place of the demodulated I s"
  "ignal at start of every data \ntransmission. This method will be demonstrated in an other model\n(BPSK_Comp_PreAmb)"
  SavedCharacterEncoding  "windows-1252"
  PreLoadFcn		  "PreLoadFcnBPSK_Real"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  CloseFcn		  "CloseFcnBPSK_Real"
  InitFcn		  "InitFcnBPSK_Real"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Thu Jan 05 14:56:24 2017"
  RTWModifiedTimeStamp	  317664650
  ModelVersionFormat	  "1.%<AutoIncrement:80>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "7.81e-008"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "FixedStepDiscrete"
	  SolverName		  "FixedStepDiscrete"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  on
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "warning"
	  UnconnectedOutputMsg	  "warning"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Solver"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      InitialStates	      "0"
      SampleTime	      "1"
      a0EqualsOne	      off
      NumCoefMin	      "[]"
      NumCoefMax	      "[]"
      DenCoefMin	      "[]"
      DenCoefMax	      "[]"
      OutMin		      "[]"
      OutMax		      "[]"
      StateDataTypeStr	      "Inherit: Same as input"
      NumCoefDataTypeStr      "Inherit: Inherit via internal rule"
      DenCoefDataTypeStr      "Inherit: Inherit via internal rule"
      NumProductDataTypeStr   "Inherit: Inherit via internal rule"
      DenProductDataTypeStr   "Inherit: Inherit via internal rule"
      NumAccumDataTypeStr     "Inherit: Inherit via internal rule"
      DenAccumDataTypeStr     "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RateTransition
      Integrity		      on
      Deterministic	      on
      X0		      "0"
      OutPortSampleTimeOpt    "Specify"
      OutPortSampleTimeMultiple	"1"
      OutPortSampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Trigonometry
      Operator		      "sin"
      ApproximationMethod     "None"
      NumberOfIterations      "11"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "BPSK_Real"
    Location		    [139, 434, 1065, 990]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "31"
    Block {
      BlockType		      Sin
      Name		      "Carrier"
      SID		      "1"
      Ports		      [0, 1]
      Position		      [15, 50, 45, 80]
      Frequency		      "2513274.1229"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F1(z)"
      SID		      "2"
      Ports		      [1, 1]
      Position		      [440, 92, 500, 128]
      Numerator		      "[1  1]"
      Denominator	      "[21.3555     -19.3555]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F2(z)"
      SID		      "3"
      Ports		      [1, 1]
      Position		      [440, 297, 500, 333]
      Numerator		      "[1  1]"
      Denominator	      "[21.3555     -19.3555]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F3(z)"
      SID		      "4"
      Ports		      [1, 1]
      Position		      [640, 172, 700, 208]
      BlockMirror	      on
      Numerator		      "[102.8559     -100.8559]"
      Denominator	      "[512 -512]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"uf(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F4(z)"
      SID		      "5"
      Ports		      [1, 1]
      Position		      [470, 184, 515, 216]
      BlockMirror	      on
      Numerator		      "[0 7.8125e-008]"
      Denominator	      "[1 -1]"
      SampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      Scope
      Name		      "I"
      SID		      "6"
      Ports		      [2]
      Position		      [815, 101, 845, 134]
      ShowName		      off
      Floating		      off
      Location		      [747, 116, 1218, 355]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.5~-1.5"
      YMax		      "1.5~1.5"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "100000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "K0"
      SID		      "7"
      Position		      [650, 245, 680, 275]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "1263309.3633"
    }
    Block {
      BlockType		      Product
      Name		      "MixerI"
      SID		      "8"
      Ports		      [2, 1]
      Position		      [365, 92, 395, 123]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MixerQ"
      SID		      "9"
      Ports		      [2, 1]
      Position		      [370, 297, 400, 328]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Modulation"
      SID		      "10"
      Ports		      [2, 1]
      Position		      [210, 82, 240, 113]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"c(t)m(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Mul"
      SID		      "11"
      Ports		      [2, 1]
      Position		      [725, 172, 755, 203]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ud"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Product1"
      SID		      "12"
      Ports		      [2, 1]
      Position		      [360, 152, 390, 183]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"LOsin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Product2"
      SID		      "13"
      Ports		      [2, 1]
      Position		      [360, 242, 390, 273]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"LOcos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Product4"
      SID		      "14"
      Ports		      [2, 1]
      Position		      [585, 182, 615, 213]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "RF Signal"
      SID		      "16"
      Ports		      [2]
      Position		      [270, 216, 300, 249]
      Floating		      off
      Location		      [763, 435, 1231, 741]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "100000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTRand"
      SID		      "27"
      Position		      [120, 124, 160, 166]
      OutPortSampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "Random"
      SID		      "17"
      Position		      [15, 129, 45, 161]
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      Relay
      Name		      "Relay"
      SID		      "18"
      Position		      [70, 130, 100, 160]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "19"
      Ports		      [2, 1]
      Position		      [540, 190, 560, 210]
      BlockMirror	      on
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"omega2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "cos"
      SID		      "22"
      Ports		      [1, 1]
      Position		      [415, 250, 445, 280]
      BlockMirror	      on
      Operator		      "cos"
    }
    Block {
      BlockType		      Constant
      Name		      "mult"
      SID		      "23"
      Position		      [415, 205, 445, 235]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "2"
    }
    Block {
      BlockType		      Constant
      Name		      "omega0"
      SID		      "24"
      Position		      [580, 245, 610, 275]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "2450442.2698"
    }
    Block {
      BlockType		      Scope
      Name		      "ph error"
      SID		      "25"
      Ports		      [3]
      Position		      [815, 16, 845, 74]
      Floating		      off
      Location		      [39, 59, 508, 467]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-0.6~2e+006~-0.6"
      YMax		      "0.6~2.7e+006~0.6"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "200000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Trigonometry
      Name		      "sin"
      SID		      "26"
      Ports		      [1, 1]
      Position		      [410, 145, 440, 175]
      BlockMirror	      on
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0]
      SrcBlock		      "Carrier"
      SrcPort		      1
      Points		      [70, 0; 0, 25]
      DstBlock		      "Modulation"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Relay"
      SrcPort		      1
      DstBlock		      "RTRand"
      DstPort		      1
    }
    Line {
      Name		      "c(t)m(t)"
      SrcBlock		      "Modulation"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	DstBlock		"RF Signal"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	Points			[65, 0]
	Branch {
	  DstBlock		  "MixerI"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 220]
	  DstBlock		  "MixerQ"
	  DstPort		  2
	}
      }
    }
    Line {
      SrcBlock		      "MixerI"
      SrcPort		      1
      DstBlock		      "F1(z)"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MixerQ"
      SrcPort		      1
      DstBlock		      "F2(z)"
      DstPort		      1
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "F1(z)"
      SrcPort		      1
      Points		      [270, 0]
      Branch {
	Points			[0, 70]
	DstBlock		"Mul"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	DstBlock		"I"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "F2(z)"
      SrcPort		      1
      Points		      [285, 0; 0, -120]
      Branch {
	DstBlock		"Mul"
	DstPort			2
      }
      Branch {
	Points			[0, -70]
	DstBlock		"I"
	DstPort			2
      }
    }
    Line {
      Name		      "ud"
      Labels		      [0, 0]
      SrcBlock		      "Mul"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	Labels			[1, 0]
	Points			[0, -125]
	DstBlock		"ph error"
	DstPort			3
      }
      Branch {
	DstBlock		"F3(z)"
	DstPort			1
      }
    }
    Line {
      Name		      "uf(t)"
      Labels		      [0, 0]
      SrcBlock		      "F3(z)"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	DstBlock		"Product4"
	DstPort			1
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -165]
	DstBlock		"ph error"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "K0"
      SrcPort		      1
      Points		      [-15, 0]
      DstBlock		      "Product4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Product4"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "omega0"
      SrcPort		      1
      Points		      [-25, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "omega2"
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	Labels			[2, 0]
	Points			[0, -155]
	DstBlock		"ph error"
	DstPort			2
      }
      Branch {
	DstBlock		"F4(z)"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "F4(z)"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	Points			[0, -40]
	DstBlock		"sin"
	DstPort			1
      }
      Branch {
	DstBlock		"cos"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "sin"
      SrcPort		      1
      DstBlock		      "Product1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "cos"
      SrcPort		      1
      DstBlock		      "Product2"
      DstPort		      2
    }
    Line {
      SrcBlock		      "mult"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	DstBlock		"Product1"
	DstPort			2
      }
      Branch {
	DstBlock		"Product2"
	DstPort			1
      }
    }
    Line {
      Name		      "LOsin"
      Labels		      [0, 0]
      SrcBlock		      "Product1"
      SrcPort		      1
      Points		      [-15, 0; 0, -55]
      DstBlock		      "MixerI"
      DstPort		      2
    }
    Line {
      Name		      "LOcos"
      Labels		      [0, 0]
      SrcBlock		      "Product2"
      SrcPort		      1
      Points		      [-10, 0; 0, 45]
      DstBlock		      "MixerQ"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Random"
      SrcPort		      1
      DstBlock		      "Relay"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RTRand"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[0, -40]
	DstBlock		"Modulation"
	DstPort			2
      }
      Branch {
	Labels			[1, 0]
	Points			[0, 95]
	DstBlock		"RF Signal"
	DstPort			2
      }
    }
    Annotation {
      Name		      "m(t)"
      Position		      [210, 235]
    }
    Annotation {
      Name		      "I,Q"
      Position		      [832, 144]
    }
    Annotation {
      Name		      "Q"
      Position		      [795, 174]
    }
    Annotation {
      Name		      "phi2"
      Position		      [465, 258]
    }
  }
}
