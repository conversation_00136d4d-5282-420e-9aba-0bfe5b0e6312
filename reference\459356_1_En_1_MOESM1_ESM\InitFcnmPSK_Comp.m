% Init Function of Model mPSK_Comp
% --------------------------------


% compute the parameters of the model

Kd = 1;           % phase detector gain of m-ary Costas loop
fT = 0.4 * fR;    % transit freq of Costas loop (nominal 40 kHz)
omegaT = 2 * pi * fT;
tau1 = 20e-6;     % integrator time constant in PI loop filter 
K0 = tau1 * omegaT^2/Kd;   % DCO gain
omega0 = 2 * pi * (fC - delta_f);
tR = 1/fR; % duration of one symbol (bit)
omegaC = 2 * pi * fC;
T = 1/(fC * 32); % sampling interval for sine generator, 32 samples per cycle
TD = D * T;

% Check the ratio of sampling times of the model. This ratio must be
% an integer number. If not, round sampling times to fulfill that
% requirement.

T_string = num2str(T, 3);
% Note: T_string is string variable of sampling time T (rounded)
T_round = str2num(T_string);
tR_string = num2str(tR, 3);
tR_round = str2num(tR_string);
K = round(tR_round/T_round); % round ratio of sampling times to integer
tR_string = num2str(K*T_round, 9);
% Note: tR_string is string variable of sampling time tR (rounded)
TD = D * T_round;
TD_string = num2str(TD, 6);
% Note: TD_string is string variable of sampling time TD (rounded)

% prewarp corner frequencies (for bilinear z transform)
omegaT_P = (2/T) * tan(omegaT * T/2);

% Setup parameters

% Enter parameters of the model
set_param('mPSK_Comp', 'FixedStep', T_string);
set_param('mPSK_Comp', 'Stop time', num2str(nCycles * tR));

% set params of Int1 (integrates omegaC to get phi1)
set_param('mPSK_Comp/Int1', 'Numerator', ['[', num2str([0, T]), ']']);
set_param('mPSK_Comp/Int1', 'Denominator', ['[', num2str([1, -1]), ']']);
set_param('mPSK_Comp/Int1', 'Sample time', T_string);

set_param('mPSK_Comp/RTSig', 'OutPortSampleTime', T_string);
set_param('mPSK_Comp/RTRot', 'OutPortSampleTime', TD_string);
set_param('mPSK_Comp/RTLO', 'OutPortSampleTime', TD_string);

set_param('mPSK_Comp/Hilbert', 'NumDelays', num2str(OS/4));

% set params of loop filter F3(z)
set_param('mPSK_Comp/F3(z)', 'Numerator', ['[', num2str([1+2/(omegaT_P*TD), 1-2/(omegaT_P*TD)]), ']']);
set_param('mPSK_Comp/F3(z)', 'Denominator', ['[', num2str([2*tau1/TD, -2*tau1/TD]), ']']);
set_param('mPSK_Comp/F3(z)', 'Sample time', TD_string);

% set params of DCO F4(z)
set_param('mPSK_Comp/F4(z)', 'Numerator', ['[', num2str([0, TD]), ']']);
set_param('mPSK_Comp/F4(z)', 'Denominator', ['[', num2str([1, -1]), ']']);
set_param('mPSK_Comp/F4(z)', 'Sample time', TD_string);

set_param('mPSK_Comp/K0', 'Value', num2str(K0));
set_param('mPSK_Comp/Rand', 'Minimum', num2str(-4));
set_param('mPSK_Comp/Rand', 'Maximum', num2str(3.99));
set_param('mPSK_Comp/Rand', 'Sample time', tR_string);

set_param('mPSK_Comp/omega0', 'Value', num2str(omega0));
set_param('mPSK_Comp/omegaC', 'Value', num2str(omegaC));
