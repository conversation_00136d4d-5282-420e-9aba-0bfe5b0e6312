Model {
  Name			  "mPSK_Comp"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.109"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model mPSK_Comp\n-------------------------\n\n1. Model description\n-----------------------\nThis mo"
  "del is derived from QPSK_Comp. While QPSK_Comp demonstrates a QPSK\ncommunication using a modified Costas loop (cf. "
  "Tretter: Communication system\ndesign using DSP algorithms), this new model demonstrates 8-ary PSK, again \nusing th"
  "e modified Costas loop. \n\na. Transmitter\n-----------------\nIn the transmitter section of the model, an 8-ary ran"
  "dom signal k(n) is created, which\nis in the range -4 ... 3. (n = sample index). Then a complex phasor exp(j k(n) pi"
  "/4) is\ncomputed. Data signal I is then given by the real part, and data signal Q is given by\nthe imaginary part of"
  " that phasor. This phasor has a magnitude of 1 and can have\n1 of 8 possible phases, i.e. 0, pi/4, pi/2, 3 pi/4 etc."
  "\nFurthermore a complex carrier exp(j omegaC t + theta1) = exp(j phi1)\nis generated. (In this simulation theta1 is "
  "set 0.) Multiplying the phasor with the\ncomplex carrier yields the pre-envelope signal s+(t)\n\n    s+(t) = (I + j "
  "Q) * exp(j omegaC*t)\n\nThe transmitter output signal s(t) is by definition the real part of s+(t)\n\n    s(t) = Re["
  "s+(t)] = I cos(omegaC t) - Q sin(omegaC t)\n\n\nb. Receiver\n-------------\nThe receiverfirst builds the pre-envelop"
  "e signal s+(t)\n\n    s+(t) = s(t) + j s^(t)\n               with s^(t) = H[s(t)]\n\nwhere H[.] denotes Hilbert tran"
  "sform.  Because the bandwidth\nof the data signal is much less than the carrier frequency, the Hilbert transform can"
  "\nbe computed by a simple delay, i.e. by delaying the samples of s(t)  by one quarter\nof a carrier period (8 sample"
  "s by default in our case). \n\nBecause the receiver does not \"know\" the exact frequency and phase of the\ncarrier,"
  " a Costas loop is used to extract that information. \nTo reconstruct the data signal, the pre-envelope signal is mul"
  "tiplied with a \ncomplex carrier of the form exp(-j omegaC t):\n\n    s~(t) = s+(t) exp(- j omegaC t) = (I + jQ) * e"
  "xp(j omegaC t) * exp(-j omegaC t)\n\nThis yields \n\n    s~(t) = I + j Q\n\nwhich is called \"complex envelope\". Wh"
  "en the loop has notyet acquired lock,\ns~(t) is not identical with the complex envelope but contains a phase error t"
  "erm:\n\n    s~(t) = (I + j Q) * exp(j theta_e)\n\nwhere theta_e is the phase error. We now have to extract that phas"
  "e error by some\nsuitable operations. First we extract the phase of s~(t) by block C->M,phi. This phase\nis called p"
  "hi_out. Next we round that phi_out to the nearest integer multiple of pi/4\n(45 degrees). This yields a phase estima"
  "te and is called phi_est here.\nNow the phase error theta_e is simply the difference \n\n    ud(t) = theta_e = phi_o"
  "ut - phi_est\n\nThe blocks Mul3, C->M,phi, *4/pi, round, *(-pi/4) end the adder form therefore\na phase detector tha"
  "t has phase detector gain Kd = 1. \n\nThe phase error signal ud(t) is applied to the input of a loop filter F3(z) th"
  "at is realized\nas a PI filter. Its transfer function in the s domain is\n\n                     1   + s tau2\n    F"
  "3(s ) = --------------------------\n                         s tau1\n\nThis transfer function is converted into F3(z"
  ") by the bilinear z transform (cf. the \ncomments in initialization file InitFcnmPSK_Comp.m).\n\nThe output of the l"
  "oop filter is applied to the input of the DCO built from F4(z) and \nsurrounding blocks. In the s domain the transfe"
  "r function of the DCO is given by\n\n                  K0\n    F4(s) = ------------\n                    s\n\nwith K"
  "0 = DCO gain. This transfer function is also converted into F4(z) by using the bilinear\nz transform (cf. the commen"
  "ts in initialization file InitFctMPSK8D.m).\n\nThe radian frequency of the DCO is given by\n\n    omega2 = omega0 + "
  "K0 uf\n\nwhere uf is the output signal of the loop filter. omega0 is the center radian frequency of\nthe Costas loop"
  ". Ideally omega0 should be equal with omegaC. It is allowed, however,\nthat omega0 deviates from omegaC, because the"
  " Costas loop can track the\nfrequency error. \n\n\n2. Parameters of the model\n--------------------------------\nA n"
  "umber of parameters can be specified by the operator:\n\n- fC Carrier frequency of the transmitter (default = 400000"
  " Hz)\n- fR Symbol rate (default = 100000 symb/s)\n- OS Oversampling factor used in the transmitter section. The samp"
  "ling frequency of\n      the model is defined as the product OS * fC. (default = 32)\n- nCycles Number of symbols us"
  "ed in the simulation (default = 20)\n- delta_f Fequency error of receiver carrier frequency. To simulate a frequency"
  " offset,\n      the initial frequency of the DCO is set to fC - delta_f.\n- D Decimation factor (default = 8). This "
  "allow to sample the blocks within the\n     receiver with a lower sampling frequency.\n\n\n3. Instructions for model"
  " operation\n----------------------------------------\nTo perform simulations, proceed as follows:\n\n- Load the mode"
  "l mPSK_Comp by double-clicking the file QPSK_Comp.mdl in the\n  Current Folder of Matlab\n\n- This displays the mode"
  "l and a figure window containing  6 edit controls for\n  parameter specification. When the model runs the first time"
  ", default parameters are\n  set (cf. section 2). You now can alter these parameters. When done, click the 'Done'\n  "
  "button. When an invalid number format has been entered in one of the edit controls,\n  an error message is issued, t"
  "elling you to correct this entry and to hit the 'Done'\n  button again. Hitting this button saves the actual paramet"
  "ers to a parameter file\n  params_mQPSK_Comp.mat. When the model is started next, the parameters saved\n  in this fi"
  "les are loaded.\n  There is an option to load the initial default parameters: hit the 'Set init defaults' button.\n "
  " This can be useful whenever you specified parameters that don't give useful results.\n\n- After hitting the 'Done' "
  "button, go to the model window and start the simulation\n  (menu item Simulation/Start).;\n\n- Look at the results o"
  "n the scopes phi_in and ph error. phi_in represents the phase\n  of the data phasor I + j Q. k(n) can have integer v"
  "alues in the range -4 ... 3, and\n  the corresponding phase is k(n) * pi/4. The reconstructed phase is seen in\n  tr"
  "ace k^(n)wrap in scope phi error. The Wrap block is used to get the same phase\n  readout at the input and at the ou"
  "tput of the model. Because the round block can\n  also yield a result of +4, this is wrapped to -4 in order to have "
  "the same scales\n  for input and output phase. \n  From the theta_e, uf and omega2 signals you can see how fast the "
  "lock acquires\n  lock.\n\n  \n4. Comment on the pull-in range of the Costas loop\n----------------------------------"
  "-------------------------\nIn contrast to the conventional Costas loop (as used in model QPSK_Real), the\nmodified C"
  "ostas loop does not require an additional lowpass filter to remove the\nunwanted double-frequency components. Hence "
  "there is no additional phase shift\nin the Costas loop, and the pull-in range can be arbitrarily high.\nWhen selecti"
  "ng a higher delta_f value, it will be necessary to increase the duration\nof the simulation (increase nCycles), beca"
  "use the pull-in time TP will become larger.\nWhen very large frequency errors are simulated, you will note that the "
  "loop is no\nlonger able to acquire lock when larger decimation factors are used. This stems from the\nfact that the "
  "output signal of block Mul3 will contain very high frequency components,\ni.e. waveforms whose frequency is up to 8 "
  "times the frequency error. To process those\nhigh-frequency signals a large sampling rate must be selected, otherwis"
  "e we will be\nconfronted with aliasing effects.\n\n\n5. Comment on the \"phase ambiguity\" of the Costas loop\n-----"
  "------------------------------------------------------------\nWhen performing simulations with different values of f"
  "requency error\ndelta_f you will recognize in some situations that the the phase of the output signal\ncan be offset"
  " from the phase of the input signal.\nThis occurs because the Costas looop for mQPSK can lock with 8 possible \nphas"
  "e differences between transmitter and receiver carriers, i. e. with a \nphase difference of 0, 45, 90, 135 etc. degr"
  "ees.\nTo avoid this ambiguity additional measures have to been taken. A common\nmethod is to use a given preamble at"
  " each start of a data transmission,\ne.g. a sequence of symbols having all the same phase.\nBecause the receiver \"k"
  "nows\" that preamble it will use the preamble \npattern in place of the demodulated I signal at start of every data "
  "\ntransmission. This method is demonstrated in an other model (BPSK_Real_PreAmb).\nThe same procedure could be appli"
  "ed in this model, i.e. we would have to \ncreate a predefined preamble for both I and Q signals, e.g. a sequence of "
  "all 1's.\n\n\n\n"
  SavedCharacterEncoding  "windows-1252"
  PreLoadFcn		  "PreLoadFcnmPSK_Comp"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  CloseFcn		  "CloseFcnmPSK_Comp"
  InitFcn		  "InitFcnmPSK_Comp"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Sun Nov 30 10:43:51 2014"
  RTWModifiedTimeStamp	  339245001
  ModelVersionFormat	  "1.%<AutoIncrement:109>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "7.81e-008"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "FixedStepDiscrete"
	  SolverName		  "FixedStepDiscrete"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  on
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "none"
	  UnconnectedOutputMsg	  "none"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Solver"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      ComplexToMagnitudeAngle
      Output		      "Magnitude and angle"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      ComplexToRealImag
      Output		      "Real and imag"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      InitialStates	      "0"
      SampleTime	      "1"
      a0EqualsOne	      off
      NumCoefMin	      "[]"
      NumCoefMax	      "[]"
      DenCoefMin	      "[]"
      DenCoefMax	      "[]"
      OutMin		      "[]"
      OutMax		      "[]"
      StateDataTypeStr	      "Inherit: Same as input"
      NumCoefDataTypeStr      "Inherit: Inherit via internal rule"
      DenCoefDataTypeStr      "Inherit: Inherit via internal rule"
      NumProductDataTypeStr   "Inherit: Inherit via internal rule"
      DenProductDataTypeStr   "Inherit: Inherit via internal rule"
      NumAccumDataTypeStr     "Inherit: Inherit via internal rule"
      DenAccumDataTypeStr     "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParamMin		      "[]"
      ParamMax		      "[]"
      ParamDataTypeStr	      "Inherit: Same as input"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Ground
    }
    Block {
      BlockType		      Math
      Operator		      "exp"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      IntermediateResultsDataTypeStr "Inherit: Inherit via internal rule"
      AlgorithmType	      "Newton-Raphson"
      Iterations	      "3"
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RateTransition
      Integrity		      on
      Deterministic	      on
      X0		      "0"
      OutPortSampleTimeOpt    "Specify"
      OutPortSampleTimeMultiple	"1"
      OutPortSampleTime	      "-1"
    }
    Block {
      BlockType		      RealImagToComplex
      Input		      "Real and imag"
      ConstantPart	      "0"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Rounding
      Operator		      "floor"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Switch
      Criteria		      "u2 >= Threshold"
      Threshold		      "0"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      ZeroCross		      on
      SampleTime	      "-1"
      AllowDiffInputSizes     off
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "mPSK_Comp"
    Location		    [105, 183, 1271, 766]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "101"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "40"
    Block {
      BlockType		      Gain
      Name		      "*(-pi/4)"
      SID		      "1"
      Position		      [890, 205, 920, 235]
      Gain		      "-pi/4"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"phi_est"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "*4/pi"
      SID		      "2"
      Position		      [775, 205, 805, 235]
      Gain		      "4/pi"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Gain
      Name		      "*pi/4"
      SID		      "3"
      Position		      [170, 250, 200, 280]
      Gain		      "pi/4"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      ComplexToMagnitudeAngle
      Name		      "C->M,phi"
      SID		      "4"
      Ports		      [1, 2]
      Position		      [710, 198, 740, 227]
      Output		      "Magnitude and angle"
      Port {
	PortNumber		2
	Name			"phi_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      ComplexToRealImag
      Name		      "C->R,I1"
      SID		      "5"
      Ports		      [1, 2]
      Position		      [380, 188, 410, 217]
      Output		      "Real and imag"
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F3(z)"
      SID		      "6"
      Ports		      [1, 1]
      Position		      [1000, 322, 1060, 358]
      BlockMirror	      on
      Numerator		      "[13.7361     -11.7361]"
      Denominator	      "[64.0205     -64.0205]"
      SampleTime	      "6.248e-007"
      Port {
	PortNumber		1
	Name			"uf(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F4(z)"
      SID		      "7"
      Ports		      [1, 1]
      Position		      [765, 331, 815, 369]
      BlockMirror	      on
      Numerator		      "[0  6.248e-007]"
      Denominator	      "[1 -1]"
      SampleTime	      "6.248e-007"
      Port {
	PortNumber		1
	Name			"phi2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Ground
      Name		      "GND"
      SID		      "8"
      Position		      [20, 400, 40, 420]
    }
    Block {
      BlockType		      Gain
      Name		      "Gain"
      SID		      "9"
      Position		      [410, 425, 440, 455]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"-j"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Reference
      Name		      "Hilbert"
      SID		      "10"
      Ports		      [1, 1]
      Position		      [450, 223, 485, 257]
      LibraryVersion	      "1.225"
      UserDataPersistent      on
      UserData		      "DataTag0"
      SourceBlock	      "simulink/Discrete/Integer Delay"
      SourceType	      "Integer Delay"
      NumDelays		      "8"
      InputProcessing	      "Inherited"
      vinit		      "0.0"
      samptime		      "-1"
      Port {
	PortNumber		1
	Name			"s^(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "Int1"
      SID		      "11"
      Ports		      [1, 1]
      Position		      [75, 341, 125, 369]
      Numerator		      "[0 7.8125e-008]"
      Denominator	      "[1 -1]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"phi1"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Inv"
      SID		      "12"
      Position		      [890, 75, 920, 105]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Constant
      Name		      "K0"
      SID		      "13"
      Position		      [1010, 390, 1040, 420]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "1263309.3633"
    }
    Block {
      BlockType		      Product
      Name		      "Mul1"
      SID		      "14"
      Ports		      [2, 1]
      Position		      [180, 347, 210, 378]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"j phi1"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Mul2"
      SID		      "15"
      Ports		      [2, 1]
      Position		      [225, 177, 255, 208]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Mul3"
      SID		      "16"
      Ports		      [2, 1]
      Position		      [655, 197, 685, 228]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s~(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Mul4"
      SID		      "17"
      Ports		      [2, 1]
      Position		      [705, 342, 735, 373]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Mul5"
      SID		      "18"
      Ports		      [2, 1]
      Position		      [930, 332, 960, 363]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Mul7"
      SID		      "19"
      Ports		      [2, 1]
      Position		      [335, 187, 365, 218]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->C2"
      SID		      "20"
      Ports		      [2, 1]
      Position		      [95, 423, 125, 452]
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"j"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "R,I->C3"
      SID		      "21"
      Ports		      [2, 1]
      Position		      [520, 188, 550, 217]
      Input		      "Real and imag"
      Port {
	PortNumber		1
	Name			"s+(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTLO"
      SID		      "22"
      Position		      [495, 419, 535, 461]
      OutPortSampleTime	      "6.248e-007"
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTRot"
      SID		      "23"
      Position		      [585, 184, 625, 226]
      OutPortSampleTime	      "6.248e-007"
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTSig"
      SID		      "24"
      Position		      [150, 164, 190, 206]
      OutPortSampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "Rand"
      SID		      "25"
      Position		      [25, 169, 55, 201]
      Minimum		      "-4"
      Maximum		      "3.99"
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "26"
      Ports		      [2, 1]
      Position		      [880, 340, 900, 360]
      BlockMirror	      on
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"omega2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum1"
      SID		      "27"
      Ports		      [2, 1]
      Position		      [940, 206, 960, 264]
      ShowName		      off
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ud(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap"
      SID		      "29"
      Position		      [945, 77, 975, 143]
      Threshold		      "3.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"k^(n)wrap"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Math
      Name		      "exp1"
      SID		      "30"
      Ports		      [1, 1]
      Position		      [245, 350, 275, 380]
      Port {
	PortNumber		1
	Name			"exp(j phi1)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Math
      Name		      "exp2"
      SID		      "31"
      Ports		      [1, 1]
      Position		      [650, 345, 680, 375]
      BlockMirror	      on
      Port {
	PortNumber		1
	Name			"exp(-j phi2)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Math
      Name		      "exp3"
      SID		      "32"
      Ports		      [1, 1]
      Position		      [275, 180, 305, 210]
    }
    Block {
      BlockType		      Rounding
      Name		      "floor"
      SID		      "33"
      Position		      [80, 170, 110, 200]
      Port {
	PortNumber		1
	Name			"k(n)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "imag"
      SID		      "34"
      Position		      [15, 440, 45, 470]
    }
    Block {
      BlockType		      Constant
      Name		      "omega0"
      SID		      "35"
      Position		      [915, 390, 945, 420]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "2450442.2698"
    }
    Block {
      BlockType		      Constant
      Name		      "omegaC"
      SID		      "36"
      Position		      [20, 340, 50, 370]
      Value		      "2513274.1229"
    }
    Block {
      BlockType		      Scope
      Name		      "ph error"
      SID		      "37"
      Ports		      [4]
      Position		      [1110, 263, 1140, 297]
      Floating		      off
      Location		      [40, 406, 677, 904]
      Open		      on
      NumInputPorts	      "4"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
	axes4			"%<SignalLabel>"
      }
      YMin		      "-0.5~-1~1e+006~-4.1"
      YMax		      "0.5~1~3e+006~3.1"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "phi_in"
      SID		      "38"
      Ports		      [1]
      Position		      [225, 53, 255, 87]
      Floating		      off
      Location		      [45, 192, 684, 342]
      Open		      on
      NumInputPorts	      "1"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
      }
      YMin		      "-4.1"
      YMax		      "3.1"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Rounding
      Name		      "round"
      SID		      "39"
      Position		      [825, 205, 855, 235]
      Operator		      "round"
      Port {
	PortNumber		1
	Name			"k^(n)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Line {
      Name		      "uf(t)"
      Labels		      [0, 0]
      SrcBlock		      "F3(z)"
      SrcPort		      1
      Points		      [-10, 0]
      Branch {
	DstBlock		"Mul5"
	DstPort			1
      }
      Branch {
	Points			[0, -65]
	DstBlock		"ph error"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "K0"
      SrcPort		      1
      Points		      [-30, 0]
      DstBlock		      "Mul5"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul5"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "omega0"
      SrcPort		      1
      Points		      [-20, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "omega2"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [-30, 0]
      Branch {
	Points			[0, -65]
	DstBlock		"ph error"
	DstPort			3
      }
      Branch {
	DstBlock		"F4(z)"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Rand"
      SrcPort		      1
      DstBlock		      "floor"
      DstPort		      1
    }
    Line {
      SrcBlock		      "omegaC"
      SrcPort		      1
      DstBlock		      "Int1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "GND"
      SrcPort		      1
      Points		      [20, 0; 0, 20]
      DstBlock		      "R,I->C2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "imag"
      SrcPort		      1
      Points		      [15, 0; 0, -10]
      DstBlock		      "R,I->C2"
      DstPort		      2
    }
    Line {
      Name		      "phi1"
      Labels		      [0, 0]
      SrcBlock		      "Int1"
      SrcPort		      1
      DstBlock		      "Mul1"
      DstPort		      1
    }
    Line {
      Name		      "j"
      SrcBlock		      "R,I->C2"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	DstBlock		"Gain"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	Points			[0, -70]
	Branch {
	  DstBlock		  "Mul1"
	  DstPort		  2
	}
	Branch {
	  Points		  [0, -105]
	  DstBlock		  "*pi/4"
	  DstPort		  1
	}
      }
    }
    Line {
      Name		      "j phi1"
      Labels		      [0, 0]
      SrcBlock		      "Mul1"
      SrcPort		      1
      DstBlock		      "exp1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RTSig"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	DstBlock		"Mul2"
	DstPort			1
      }
      Branch {
	Points			[0, -115]
	DstBlock		"phi_in"
	DstPort			1
      }
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0]
      SrcBlock		      "C->R,I1"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	DstBlock		"Hilbert"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	DstBlock		"R,I->C3"
	DstPort			1
      }
    }
    Line {
      Name		      "s^(t)"
      Labels		      [0, 0]
      SrcBlock		      "Hilbert"
      SrcPort		      1
      Points		      [0, -30]
      DstBlock		      "R,I->C3"
      DstPort		      2
    }
    Line {
      Name		      "s+(t)"
      Labels		      [0, 0]
      SrcBlock		      "R,I->C3"
      SrcPort		      1
      DstBlock		      "RTRot"
      DstPort		      1
    }
    Line {
      Name		      "phi2"
      Labels		      [0, 0]
      SrcBlock		      "F4(z)"
      SrcPort		      1
      DstBlock		      "Mul4"
      DstPort		      1
    }
    Line {
      Name		      "-j"
      Labels		      [0, 0]
      SrcBlock		      "Gain"
      SrcPort		      1
      DstBlock		      "RTLO"
      DstPort		      1
    }
    Line {
      Name		      "exp(-j phi2)"
      Labels		      [0, 1]
      SrcBlock		      "exp2"
      SrcPort		      1
      Points		      [-5, 0]
      DstBlock		      "Mul3"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul4"
      SrcPort		      1
      DstBlock		      "exp2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RTRot"
      SrcPort		      1
      DstBlock		      "Mul3"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RTLO"
      SrcPort		      1
      Points		      [210, 0]
      DstBlock		      "Mul4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "*pi/4"
      SrcPort		      1
      Points		      [0, -65]
      DstBlock		      "Mul2"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Mul2"
      SrcPort		      1
      DstBlock		      "exp3"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Mul7"
      SrcPort		      1
      DstBlock		      "C->R,I1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "exp3"
      SrcPort		      1
      DstBlock		      "Mul7"
      DstPort		      1
    }
    Line {
      Name		      "exp(j phi1)"
      Labels		      [0, 0]
      SrcBlock		      "exp1"
      SrcPort		      1
      Points		      [35, 0; 0, -155]
      DstBlock		      "Mul7"
      DstPort		      2
    }
    Line {
      Name		      "s~(t)"
      Labels		      [0, 0]
      SrcBlock		      "Mul3"
      SrcPort		      1
      DstBlock		      "C->M,phi"
      DstPort		      1
    }
    Line {
      Name		      "k(n)"
      Labels		      [0, 0]
      SrcBlock		      "floor"
      SrcPort		      1
      DstBlock		      "RTSig"
      DstPort		      1
    }
    Line {
      Name		      "phi_out"
      Labels		      [0, 0]
      SrcBlock		      "C->M,phi"
      SrcPort		      2
      Points		      [5, 0]
      Branch {
	DstBlock		"*4/pi"
	DstPort			1
      }
      Branch {
	Points			[0, 30]
	DstBlock		"Sum1"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "*4/pi"
      SrcPort		      1
      DstBlock		      "round"
      DstPort		      1
    }
    Line {
      Name		      "k^(n)"
      Labels		      [0, 0]
      SrcBlock		      "round"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"*(-pi/4)"
	DstPort			1
      }
      Branch {
	Points			[0, -90]
	Branch {
	  DstBlock		  "Wrap"
	  DstPort		  3
	}
	Branch {
	  Points		  [0, -20]
	  Branch {
	    DstBlock		    "Wrap"
	    DstPort		    2
	  }
	  Branch {
	    Points		    [0, -20]
	    DstBlock		    "Inv"
	    DstPort		    1
	  }
	}
      }
    }
    Line {
      Name		      "phi_est"
      Labels		      [0, 0]
      SrcBlock		      "*(-pi/4)"
      SrcPort		      1
      DstBlock		      "Sum1"
      DstPort		      1
    }
    Line {
      Name		      "ud(t)"
      Labels		      [0, 0]
      SrcBlock		      "Sum1"
      SrcPort		      1
      Points		      [115, 0; 0, 30]
      Branch {
	Points			[0, 75]
	DstBlock		"F3(z)"
	DstPort			1
      }
      Branch {
	DstBlock		"ph error"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Inv"
      SrcPort		      1
      DstBlock		      "Wrap"
      DstPort		      1
    }
    Line {
      Name		      "k^(n)wrap"
      Labels		      [0, 0]
      SrcBlock		      "Wrap"
      SrcPort		      1
      Points		      [35, 0; 0, 185]
      DstBlock		      "ph error"
      DstPort		      4
    }
    Annotation {
      Name		      "Local carrier"
      Position		      [661, 396]
    }
    Annotation {
      Name		      "DCO is built from integrator\nF4(z) and surrounding blocks"
      Position		      [851, 447]
    }
    Annotation {
      Name		      "Loop filter F3(z)"
      Position		      [1096, 376]
    }
    Annotation {
      Name		      "Iest,Qest = estimate\n of received symbol I, Q"
      Position		      [781, 284]
    }
    Annotation {
      Name		      "phase detector\ntheta_e = phi_out - phi_est"
      Position		      [1064, 166]
    }
    Annotation {
      Name		      "Rotator"
      Position		      [660, 182]
    }
    Annotation {
      Name		      "Create 8-ary random signal k(n)"
      Position		      [129, 34]
    }
    Annotation {
      Name		      "Create transmitter carrier"
      Position		      [186, 318]
    }
    Annotation {
      Name		      "Modulator"
      Position		      [329, 152]
    }
    Annotation {
      Name		      "Demodulator"
      Position		      [645, 135]
    }
  }
}
MatData {
  NumRecords		  1
  DataRecord {
    Tag			    DataTag0
    Data		    "  %)30     .    B     8    (     @         %    \"     $    !     0         %  0 $P    $    3    :&%S26"
    "YH97)I=&5D3W!T:6]N        #@   #     &    \"     D\"        !0    @    !     0    $          @ !  $    "
  }
}
