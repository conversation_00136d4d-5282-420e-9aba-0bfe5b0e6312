Model {
  Name			  "QPSK5"
  Version		  6.5
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.100"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model QPSK5\n----------------------\n\nThis model i"
"s the quadrature equivalent of the BPSK demodulator of model BPSK5.\nIn case "
"of QPSK the (I,Q) vector at the output should be at 4 stable positions, i.e."
"\nat angles of 45, 135, 225, and 315 degrees, respectively. The decision whet"
"her to\nrotate the vector (I,Q) forward or backward must be made differently "
"in 8 octants.\n\nWhereas model BPSK5 used a rotation increment of 22.5 degree"
"s, this turned out\nthat using this increment leads to decision errors. It ha"
"s therefore to be reduced to\nhalf that value, i.e. 11.25 degrees (pi/16). Th"
"is reduces the maximum pull-in \nfrequency by a factor of 2. Using a clock fr"
"equency of 1.6 MHz for the up/down\ncounter, the theoretical pull-in range be"
"comes 50 kHz. This value cannot be realized,\nhowever; the simulations give a"
" value of approx. 45 kHz.\n\nThe alternate model QPSK5A is less prone to deci"
"sion errors because here vector\npositions of 0, 90, 180, and 270 degrees hav"
"e been chosen. At these positions,\none vector component (e.g. I) is at its m"
"aximum, i.e. sqrt(2), while the other \ncomponent (Q) is near 0. Here larger "
"phase errors are tolerated.\nAs a consequence, model QPSK5A can operate with "
"an increment of 22.5 degrees\nat full speed (i.e. pull-in range = 100 kHz). "
  SavedCharacterEncoding  "windows-1252"
  SaveDefaultBlockParams  on
  SampleTimeColors	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  InitFcn		  "StartFunctionQPSK5"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Tue Aug 28 15:50:05 2007"
  ModelVersionFormat	  "1.%<AutoIncrement:100>"
  ConfigurationManager	  "None"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ProdHWDeviceType	  "32-bit Generic"
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.2.0"
      Array {
	Type			"Handle"
	Dimension		7
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.2.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "auto"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  Solver		  "ode45"
	  SolverName		  "ode45"
	  ZeroCrossControl	  "UseLocalSettings"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  RateTranMode		  "Deterministic"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.2.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Array {
	    Type		    "Cell"
	    Dimension		    5
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "InitFltsAndDblsToZero"
	    Cell		    "OptimizeModelRefInitCode"
	    Cell		    "NoFixptDivByZeroProtection"
	    PropName		    "DisabledProps"
	  }
	  Version		  "1.2.0"
	  BlockReduction	  on
	  BooleanDataType	  off
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnforceIntegerDowncast  on
	  ExpressionFolding	  on
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  ParameterPooling	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  BufferReusableBoundary  on
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.2.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  CheckSSInitialOutputMsg on
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "warning"
	  UnconnectedOutputMsg	  "warning"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  ModelReferenceSimTargetVerbose off
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StrictBusMsg		  "Warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.2.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.2.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  ModelReferenceNumInstancesAllowed "Multi"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  8
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  Version		  "1.2.0"
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      9
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      Version		      "1.2.0"
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      IncDataTypeInIds	      off
	      PrefixModelToSubsysFcnNames on
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      SimulinkBlockComments   on
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      10
	      Array {
		Type			"Cell"
		Dimension		13
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonFinite"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		Cell			"SupportNonInlinedSFcns"
		PropName		"DisabledProps"
	      }
	      Version		      "1.2.0"
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      GenFloatMathFcnCalls    "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      IncludeMdlTerminateFcn  on
	      CombineOutputUpdateFcns off
	      SuppressErrorStatus     off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      SimulationMode	      "normal"
      CurrentDlgPage	      "Optimization"
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    Orientation		    "right"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
  }
  BlockParameterDefaults {
    Block {
      BlockType		      Abs
    }
    Block {
      BlockType		      CombinatorialLogic
      TruthTable	      "[0 0;0 1;0 1;1 0;0 1;1 0;1 0;1 1]"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      SampleTime	      "1"
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
      Realization	      "auto"
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParameterDataTypeMode   "Same as input"
      ParameterDataType	      "sfix(16)"
      ParameterScalingMode    "Best Precision: Matrix-wise"
      ParameterScaling	      "2^0"
      OutDataTypeMode	      "Same as input"
      OutDataType	      "sfix(16)"
      OutScaling	      "2^0"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Mux
      Inputs		      "4"
      DisplayOption	      "none"
      UseBusObject	      off
      BusObject		      "BusObject"
      NonVirtualBus	      off
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      InputSameDT	      on
      OutDataTypeMode	      "Same as first input"
      OutDataType	      "sfix(16)"
      OutScaling	      "2^0"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutputDataTypeScalingMode	"All ports same datatype"
      OutDataType	      "sfix(16)"
      OutScaling	      "2^0"
      ConRadixGroup	      "Use specified scaling"
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      InputSameDT	      on
      OutDataTypeMode	      "Same as first input"
      OutDataType	      "sfix(16)"
      OutScaling	      "2^0"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Switch
      Criteria		      "u2 >= Threshold"
      Threshold		      "0"
      InputSameDT	      on
      OutDataTypeMode	      "Inherit via internal rule"
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Trigonometry
      Operator		      "sin"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      UnitDelay
      X0		      "0"
      SampleTime	      "1"
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  System {
    Name		    "QPSK5"
    Location		    [498, 96, 1197, 469]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    Block {
      BlockType		      Abs
      Name		      "AbsI"
      Position		      [820, 160, 850, 190]
      SaturateOnIntegerOverflow	off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Abs
      Name		      "AbsQ"
      Position		      [820, 310, 850, 340]
      SaturateOnIntegerOverflow	off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sum
      Name		      "AddI1"
      Ports		      [2, 1]
      Position		      [760, 157, 790, 188]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      OutScaling	      "2^-10"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"I'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "AddQ1"
      Ports		      [2, 1]
      Position		      [765, 307, 795, 338]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      OutScaling	      "2^-10"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Q'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierI"
      Ports		      [0, 1]
      Position		      [35, 55, 65, 85]
      SineType		      "Time based"
      Frequency		      "2513274.1229"
      Phase		      "pi/2"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierQ"
      Ports		      [0, 1]
      Position		      [35, 165, 65, 195]
      SineType		      "Time based"
      Frequency		      "2513274.1229"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "ConvI'"
      Position		      [815, 255, 845, 285]
      Port {
	PortNumber		1
	Name			"Ib'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "ConvQ'"
      Position		      [825, 370, 855, 400]
      Port {
	PortNumber		1
	Name			"Qb'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "CosGen"
      Ports		      [1, 1]
      Position		      [580, 545, 610, 575]
      Operator		      "cos"
      Port {
	PortNumber		1
	Name			"cos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "CounterCLK"
      Position		      [125, 535, 155, 565]
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutDataTypeMode	      "Inherit from 'Constant value'"
      OutDataType	      "sfix(16)"
      ConRadixGroup	      "Use specified scaling"
      OutScaling	      "2^0"
      SampleTime	      "6.25e-007"
      FramePeriod	      "inf"
    }
    Block {
      BlockType		      UnitDelay
      Name		      "Delay"
      Position		      [345, 638, 380, 672]
      Orientation	      "left"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UnitDelay
      Name		      "DelayQ"
      Position		      [160, 443, 195, 477]
      SampleTime	      "-1"
      Port {
	PortNumber		1
	Name			"U/D"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F1(z)"
      Position		      [465, 122, 525, 158]
      Numerator		      "[1  1]"
      Denominator	      "[2 -2.2204e-016]"
      SampleTime	      "6.25e-007"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F2(z)"
      Position		      [465, 327, 525, 363]
      Numerator		      "[1  1]"
      Denominator	      "[2 -2.2204e-016]"
      SampleTime	      "6.25e-007"
      Port {
	PortNumber		1
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "GainSin"
      Position		      [640, 625, 670, 655]
      Gain		      "-1"
      ParameterDataTypeMode   "Inherit via internal rule"
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Relay
      Name		      "I'>Q'"
      Position		      [940, 170, 970, 200]
      Port {
	PortNumber		1
	Name			"C"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Inverter"
      Position		      [195, 575, 225, 605]
      Gain		      "-1"
      ParameterDataTypeMode   "Inherit via internal rule"
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "Is,Qs"
      Ports		      [2]
      Position		      [495, 231, 525, 264]
      Orientation	      "left"
      Floating		      off
      Location		      [-16, 69, 308, 308]
      Open		      off
      NumInputPorts	      "2"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData3"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "LoLim"
      Position		      [300, 475, 330, 505]
      Value		      "0"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutDataTypeMode	      "Inherit from 'Constant value'"
      OutDataType	      "sfix(16)"
      ConRadixGroup	      "Use specified scaling"
      OutScaling	      "2^0"
      SampleTime	      "inf"
      FramePeriod	      "inf"
    }
    Block {
      BlockType		      Product
      Name		      "MixerI"
      Ports		      [2, 1]
      Position		      [395, 122, 425, 153]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MixerQ"
      Ports		      [2, 1]
      Position		      [400, 327, 430, 358]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "ModulC"
      Ports		      [2, 1]
      Position		      [185, 82, 215, 113]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "ModulS"
      Ports		      [2, 1]
      Position		      [190, 172, 220, 203]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI11"
      Ports		      [2, 1]
      Position		      [695, 132, 725, 163]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI12"
      Ports		      [2, 1]
      Position		      [695, 192, 725, 223]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ11"
      Ports		      [2, 1]
      Position		      [695, 262, 725, 293]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ12"
      Ports		      [2, 1]
      Position		      [695, 337, 725, 368]
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Mux
      Name		      "Mux"
      Ports		      [3, 1]
      Position		      [1010, 234, 1015, 306]
      ShowName		      off
      Inputs		      "3"
      DisplayOption	      "bar"
    }
    Block {
      BlockType		      Scope
      Name		      "Output"
      Ports		      [4]
      Position		      [255, 282, 285, 343]
      Floating		      off
      Location		      [25, 450, 480, 888]
      Open		      on
      NumInputPorts	      "4"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
	axes4			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-0.1~-5"
      YMax		      "1.1~1.1~1.1~35"
      SaveName		      "ScopeData6"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "RFSignal"
      Ports		      [3]
      Position		      [260, 219, 290, 251]
      Floating		      off
      Location		      [28, 117, 484, 364]
      Open		      on
      NumInputPorts	      "3"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-1.5"
      YMax		      "1.1~1.1~1.5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomI"
      Position		      [35, 109, 65, 141]
      SampleTime	      "1e-005"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomQ"
      Position		      [35, 219, 65, 251]
      Seed		      "1"
      SampleTime	      "1e-005"
    }
    Block {
      BlockType		      CombinatorialLogic
      Name		      "RotDir"
      Position		      [1040, 255, 1070, 285]
      TruthTable	      "[0;1;1;0;1;0;0;1]"
    }
    Block {
      BlockType		      Relay
      Name		      "SatC"
      Position		      [110, 110, 140, 140]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m1"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "SatS"
      Position		      [110, 220, 140, 250]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Select\nUp/Down"
      Position		      [255, 545, 285, 575]
      Threshold		      "0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Counter\nInput"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "SinGen"
      Ports		      [1, 1]
      Position		      [580, 625, 610, 655]
      Port {
	PortNumber		1
	Name			"sin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      Ports		      [2, 1]
      Position		      [315, 550, 335, 570]
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ACC\nout"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum1"
      Ports		      [2, 1]
      Position		      [265, 111, 295, 149]
      ShowName		      off
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"x(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum2"
      Ports		      [2, 1]
      Position		      [885, 166, 915, 204]
      ShowName		      off
      Inputs		      "+-"
      InputSameDT	      off
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Constant
      Name		      "UpLim"
      Position		      [390, 590, 420, 620]
      Value		      "31"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutDataTypeMode	      "Inherit from 'Constant value'"
      OutDataType	      "sfix(16)"
      ConRadixGroup	      "Use specified scaling"
      OutScaling	      "2^0"
      SampleTime	      "inf"
      FramePeriod	      "inf"
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap<0"
      Position		      [455, 545, 485, 575]
      Threshold		      "-0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"C_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap>31"
      Position		      [385, 535, 415, 565]
      Threshold		      "31.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Sin
      Name		      "cos"
      Ports		      [0, 1]
      Position		      [400, 185, 430, 215]
      Orientation	      "left"
      SineType		      "Time based"
      Amplitude		      "2"
      Frequency		      "2211681.2281"
      Phase		      "pi/2"
      SampleTime	      "8.8778e-008"
      Port {
	PortNumber		1
	Name			"LOcos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "cos/sin"
      Ports		      [2]
      Position		      [710, 576, 740, 609]
      Floating		      off
      Location		      [29, 779, 353, 1018]
      Open		      off
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Gain
      Name		      "pi/16"
      Position		      [520, 545, 550, 575]
      Gain		      "pi/16"
      ParameterDataTypeMode   "Inherit via internal rule"
      OutDataTypeMode	      "Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"phi_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "sin"
      Ports		      [0, 1]
      Position		      [400, 270, 430, 300]
      Orientation	      "left"
      SineType		      "Time based"
      Amplitude		      "2"
      Frequency		      "2211681.2281"
      SampleTime	      "8.8778e-008"
      Port {
	PortNumber		1
	Name			"LOsin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0]
      SrcBlock		      "CarrierI"
      SrcPort		      1
      Points		      [85, 0; 0, 20]
      DstBlock		      "ModulC"
      DstPort		      1
    }
    Line {
      Name		      "m1"
      Labels		      [0, 0]
      SrcBlock		      "SatC"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, -20]
	DstBlock		"ModulC"
	DstPort			2
      }
      Branch {
	Points			[15, 0; 0, 100]
	DstBlock		"RFSignal"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "RandomI"
      SrcPort		      1
      DstBlock		      "SatC"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MixerI"
      SrcPort		      1
      DstBlock		      "F1(z)"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MixerQ"
      SrcPort		      1
      DstBlock		      "F2(z)"
      DstPort		      1
    }
    Line {
      Name		      "LOcos"
      Labels		      [0, 0]
      SrcBlock		      "cos"
      SrcPort		      1
      Points		      [-25, 0; 0, -55]
      DstBlock		      "MixerI"
      DstPort		      2
    }
    Line {
      Name		      "LOsin"
      Labels		      [0, 0]
      SrcBlock		      "sin"
      SrcPort		      1
      Points		      [-20, 0; 0, 50]
      DstBlock		      "MixerQ"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Delay"
      SrcPort		      1
      Points		      [-15, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "ACC\nout"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Labels			[1, 0]
	DstBlock		"Wrap>31"
	DstPort			3
      }
      Branch {
	Points			[0, -10]
	DstBlock		"Wrap>31"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "LoLim"
      SrcPort		      1
      Points		      [15, 0; 0, 50]
      DstBlock		      "Wrap>31"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Wrap>31"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Wrap<0"
	DstPort			1
      }
      Branch {
	Points			[0, 10]
	DstBlock		"Wrap<0"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "UpLim"
      SrcPort		      1
      Points		      [0, -35]
      DstBlock		      "Wrap<0"
      DstPort		      3
    }
    Line {
      Name		      "C_out"
      Labels		      [0, 0]
      SrcBlock		      "Wrap<0"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	Points			[0, 95]
	DstBlock		"Delay"
	DstPort			1
      }
      Branch {
	Points			[0, -40; -255, 0]
	DstBlock		"Output"
	DstPort			4
      }
      Branch {
	DstBlock		"pi/16"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Inverter"
      SrcPort		      1
      Points		      [10, 0]
      DstBlock		      "Select\nUp/Down"
      DstPort		      3
    }
    Line {
      Name		      "Counter\nInput"
      Labels		      [0, 0]
      SrcBlock		      "Select\nUp/Down"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "CounterCLK"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, 40]
	DstBlock		"Inverter"
	DstPort			1
      }
      Branch {
	DstBlock		"Select\nUp/Down"
	DstPort			1
      }
    }
    Line {
      Name		      "sin"
      Labels		      [0, 0]
      SrcBlock		      "SinGen"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"GainSin"
	DstPort			1
      }
      Branch {
	Points			[0, -40]
	Branch {
	  DstBlock		  "cos/sin"
	  DstPort		  2
	}
	Branch {
	  Points		  [0, -315]
	  DstBlock		  "MulQ11"
	  DstPort		  2
	}
      }
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "F1(z)"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[20, 0]
	Branch {
	  Labels		  [1, 0]
	  DstBlock		  "MulI11"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 130]
	  DstBlock		  "MulQ11"
	  DstPort		  1
	}
      }
      Branch {
	Points			[0, 100]
	DstBlock		"Is,Qs"
	DstPort			1
      }
    }
    Line {
      Name		      "cos"
      Labels		      [1, 1]
      SrcBlock		      "CosGen"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[0, -200]
	Branch {
	  Points		  [0, -205]
	  DstBlock		  "MulI11"
	  DstPort		  2
	}
	Branch {
	  DstBlock		  "MulQ12"
	  DstPort		  2
	}
      }
      Branch {
	Points			[0, 25]
	DstBlock		"cos/sin"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "F2(z)"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Labels			[1, 0]
	Points			[100, 0]
	Branch {
	  Points		  [0, -145]
	  DstBlock		  "MulI12"
	  DstPort		  1
	}
	Branch {
	  DstBlock		  "MulQ12"
	  DstPort		  1
	}
      }
      Branch {
	Points			[0, -90]
	DstBlock		"Is,Qs"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "GainSin"
      SrcPort		      1
      Points		      [5, 0]
      DstBlock		      "MulI12"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulI11"
      SrcPort		      1
      Points		      [5, 0; 0, 15]
      DstBlock		      "AddI1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulI12"
      SrcPort		      1
      Points		      [5, 0; 0, -30]
      DstBlock		      "AddI1"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulQ11"
      SrcPort		      1
      Points		      [10, 0; 0, 35]
      DstBlock		      "AddQ1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulQ12"
      SrcPort		      1
      Points		      [10, 0; 0, -25]
      DstBlock		      "AddQ1"
      DstPort		      2
    }
    Line {
      Name		      "phi_out"
      Labels		      [0, 0]
      SrcBlock		      "pi/16"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"CosGen"
	DstPort			1
      }
      Branch {
	Points			[0, 80]
	DstBlock		"SinGen"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "RandomQ"
      SrcPort		      1
      DstBlock		      "SatS"
      DstPort		      1
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0]
      SrcBlock		      "CarrierQ"
      SrcPort		      1
      DstBlock		      "ModulS"
      DstPort		      1
    }
    Line {
      Name		      "m2"
      SrcBlock		      "SatS"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Labels			[1, 0]
	Points			[0, -40]
	DstBlock		"ModulS"
	DstPort			2
      }
      Branch {
	DstBlock		"RFSignal"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "ModulS"
      SrcPort		      1
      Points		      [15, 0; 0, -50]
      DstBlock		      "Sum1"
      DstPort		      2
    }
    Line {
      SrcBlock		      "ModulC"
      SrcPort		      1
      Points		      [20, 0; 0, 20]
      DstBlock		      "Sum1"
      DstPort		      1
    }
    Line {
      Name		      "x(t)"
      Labels		      [0, 0]
      SrcBlock		      "Sum1"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	DstBlock		"MixerI"
	DstPort			1
      }
      Branch {
	Points			[0, 140]
	Branch {
	  Points		  [0, 80]
	  DstBlock		  "MixerQ"
	  DstPort		  2
	}
	Branch {
	  Points		  [-80, 0]
	  DstBlock		  "RFSignal"
	  DstPort		  3
	}
      }
    }
    Line {
      Name		      "I'"
      Labels		      [0, 0]
      SrcBlock		      "AddI1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"AbsI"
	DstPort			1
      }
      Branch {
	DstBlock		"ConvI'"
	DstPort			1
      }
      Branch {
	Points			[0, -115; -575, 0; 0, 230]
	DstBlock		"Output"
	DstPort			1
      }
    }
    Line {
      Name		      "Q'"
      Labels		      [0, 0]
      SrcBlock		      "AddQ1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"AbsQ"
	DstPort			1
      }
      Branch {
	Points			[0, 60]
	Branch {
	  DstBlock		  "ConvQ'"
	  DstPort		  1
	}
	Branch {
	  Points		  [-595, 0; 0, -80]
	  DstBlock		  "Output"
	  DstPort		  2
	}
      }
    }
    Line {
      SrcBlock		      "AbsI"
      SrcPort		      1
      DstBlock		      "Sum2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Sum2"
      SrcPort		      1
      DstBlock		      "I'>Q'"
      DstPort		      1
    }
    Line {
      SrcBlock		      "AbsQ"
      SrcPort		      1
      Points		      [5, 0; 0, -130]
      DstBlock		      "Sum2"
      DstPort		      2
    }
    Line {
      Name		      "C"
      Labels		      [2, 0]
      SrcBlock		      "I'>Q'"
      SrcPort		      1
      Points		      [10, 0; 0, 60]
      DstBlock		      "Mux"
      DstPort		      1
    }
    Line {
      Name		      "Ib'"
      Labels		      [0, 0]
      SrcBlock		      "ConvI'"
      SrcPort		      1
      DstBlock		      "Mux"
      DstPort		      2
    }
    Line {
      Name		      "Qb'"
      Labels		      [0, 0]
      SrcBlock		      "ConvQ'"
      SrcPort		      1
      Points		      [65, 0; 0, -90]
      DstBlock		      "Mux"
      DstPort		      3
    }
    Line {
      SrcBlock		      "Mux"
      SrcPort		      1
      DstBlock		      "RotDir"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RotDir"
      SrcPort		      1
      Points		      [10, 0; 0, 165; -940, 0]
      DstBlock		      "DelayQ"
      DstPort		      1
    }
    Line {
      Name		      "U/D"
      Labels		      [0, 0]
      SrcBlock		      "DelayQ"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[0, 100]
	DstBlock		"Select\nUp/Down"
	DstPort			2
      }
      Branch {
	Points			[0, -140]
	DstBlock		"Output"
	DstPort			3
      }
    }
    Annotation {
      Name		      "AddI+Q"
      Position		      [276, 159]
      UseDisplayTextAsClickCallback off
    }
  }
}
