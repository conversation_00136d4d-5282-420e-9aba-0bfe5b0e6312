Model {
  Name			  "QPSK6"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.107"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model QPSK5\n----------------------\n\nThis model is similar to model QPSK5, but it operates with th"
  "e pre-envelope signal.\nThe transmitte generates directly the pre-envelope signal, which can be represented\nby\n\n "
  "   s+(t) = (m1 + j m2) exp(j [w1 t + theta1])\n\nwhere m1 and m2 are the two binary signals (I and Q), w1 is the rad"
  "ian frequency of\nthe carrier and theta1 is the zero phase.\n\nThe local oscillator within the receiver section gene"
  "rates a complex signal\n\n    u2(t) = exp(-[j w2 t + theta2])\n\nwith w2 is (fixed) radian frequency of the local os"
  "cillator and theta2 is the zero phase.\nThe two complex signals s+(t) and u2(t) are multiplied by block Product1.\nT"
  "he output signal of this block is a phasor P rotating with a frequency which is given\nby the difference w1 - w2. Th"
  "e phasor rotator must cancel this rotation by rotating \nP in the opposite direction. The phasor rrotator is control"
  "led in such a such a way that\nthe rotated phasor P settles at one of 4 stable positions, ie.e at either 45, 135, 22"
  "5, or \n315 degrees. The rotator rotates phasor P in increments of 11.25 degrees.\nThe clock frequency of the phasor"
  " rotator is chosen 12.8 MHz in this model (32 times\nthe carrier frequency, nominal 400 kHz). Consequently the overs"
  "ampling factor OS of\nthis model is 12.8 MHz/100 kHz = 128. (Note that the symbol rate is nominally 100'000\nbis/s)."
  " This results in a pull-in range delta_f_P of 400 kHz.\n\n"
  SavedCharacterEncoding  "windows-1252"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  InitFcn		  "StartFunctionQPSK6"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Tue Jan 31 14:36:44 2017"
  RTWModifiedTimeStamp	  407774200
  ModelVersionFormat	  "1.%<AutoIncrement:107>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "auto"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "ode45"
	  SolverName		  "ode45"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  off
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "warning"
	  UnconnectedOutputMsg	  "warning"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Optimization"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      Abs
      ZeroCross		      on
      SampleTime	      "-1"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
    }
    Block {
      BlockType		      CombinatorialLogic
      TruthTable	      "[0 0;0 1;0 1;1 0;0 1;1 0;1 0;1 1]"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      ComplexToRealImag
      Output		      "Real and imag"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParamMin		      "[]"
      ParamMax		      "[]"
      ParamDataTypeStr	      "Inherit: Same as input"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Memory
      X0		      "0"
      InheritSampleTime	      off
      LinearizeMemory	      off
      LinearizeAsDelay	      off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Mux
      Inputs		      "4"
      DisplayOption	      "none"
      UseBusObject	      off
      BusObject		      "BusObject"
      NonVirtualBus	      off
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RealImagToComplex
      Input		      "Real and imag"
      ConstantPart	      "0"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Switch
      Criteria		      "u2 >= Threshold"
      Threshold		      "0"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      ZeroCross		      on
      SampleTime	      "-1"
      AllowDiffInputSizes     off
    }
    Block {
      BlockType		      Trigonometry
      Operator		      "sin"
      ApproximationMethod     "None"
      NumberOfIterations      "11"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "QPSK6"
    Location		    [48, 132, 1167, 841]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "55"
    Block {
      BlockType		      Abs
      Name		      "AbsI"
      SID		      "1"
      Position		      [820, 160, 850, 190]
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Abs
      Name		      "AbsQ"
      SID		      "2"
      Position		      [820, 310, 850, 340]
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Sum
      Name		      "AddI1"
      SID		      "3"
      Ports		      [2, 1]
      Position		      [760, 157, 790, 188]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"I'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "AddQ1"
      SID		      "4"
      Ports		      [2, 1]
      Position		      [765, 307, 795, 338]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Q'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      ComplexToRealImag
      Name		      "C->Re.Im"
      SID		      "51"
      Ports		      [1, 2]
      Position		      [495, 58, 525, 87]
      Output		      "Real and imag"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
      Port {
	PortNumber		2
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierI"
      SID		      "5"
      Ports		      [0, 1]
      Position		      [35, 140, 65, 170]
      Frequency		      "2513274.1229"
      Phase		      "pi/2"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierQ"
      SID		      "6"
      Ports		      [0, 1]
      Position		      [35, 200, 65, 230]
      Frequency		      "2513274.1229"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "ConvI'"
      SID		      "7"
      Position		      [815, 255, 845, 285]
      Port {
	PortNumber		1
	Name			"Ib'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "ConvQ'"
      SID		      "8"
      Position		      [825, 370, 855, 400]
      Port {
	PortNumber		1
	Name			"Qb'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "CosGen"
      SID		      "9"
      Ports		      [1, 1]
      Position		      [580, 545, 610, 575]
      Operator		      "cos"
      Port {
	PortNumber		1
	Name			"cos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "CounterCLK"
      SID		      "10"
      Position		      [125, 535, 155, 565]
      SampleTime	      "6.25e-007"
    }
    Block {
      BlockType		      Gain
      Name		      "GainSin"
      SID		      "15"
      Position		      [640, 625, 670, 655]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Relay
      Name		      "I'>Q'"
      SID		      "16"
      Position		      [940, 170, 970, 200]
      Port {
	PortNumber		1
	Name			"C"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Inverter"
      SID		      "17"
      Position		      [195, 575, 225, 605]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "Is,Qs"
      SID		      "18"
      Ports		      [2]
      Position		      [520, 131, 550, 164]
      BlockMirror	      on
      Floating		      off
      Location		      [-16, 69, 308, 308]
      Open		      off
      NumInputPorts	      "2"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData3"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "LoLim"
      SID		      "19"
      Position		      [300, 475, 330, 505]
      Value		      "0"
    }
    Block {
      BlockType		      Memory
      Name		      "Memory"
      SID		      "54"
      Position		      [165, 445, 195, 475]
      Port {
	PortNumber		1
	Name			"U/D"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Memory
      Name		      "Memory1"
      SID		      "55"
      Position		      [350, 640, 380, 670]
      BlockMirror	      on
    }
    Block {
      BlockType		      Product
      Name		      "MulI11"
      SID		      "24"
      Ports		      [2, 1]
      Position		      [695, 132, 725, 163]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI12"
      SID		      "25"
      Ports		      [2, 1]
      Position		      [695, 192, 725, 223]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ11"
      SID		      "26"
      Ports		      [2, 1]
      Position		      [695, 262, 725, 293]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ12"
      SID		      "27"
      Ports		      [2, 1]
      Position		      [695, 337, 725, 368]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Mux
      Name		      "Mux"
      SID		      "28"
      Ports		      [3, 1]
      Position		      [1010, 234, 1015, 306]
      ShowName		      off
      Inputs		      "3"
      DisplayOption	      "bar"
    }
    Block {
      BlockType		      Scope
      Name		      "Output"
      SID		      "29"
      Ports		      [4]
      Position		      [300, 282, 330, 343]
      Floating		      off
      Location		      [18, 454, 473, 892]
      Open		      on
      NumInputPorts	      "4"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
	axes4			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-0.1~-5"
      YMax		      "1.1~1.1~1.1~35"
      SaveName		      "ScopeData6"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Product
      Name		      "Product"
      SID		      "50"
      Ports		      [2, 1]
      Position		      [285, 47, 315, 78]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Product1"
      SID		      "53"
      Ports		      [2, 1]
      Position		      [385, 57, 415, 88]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "RFSignal"
      SID		      "30"
      Ports		      [2]
      Position		      [200, 116, 230, 149]
      Floating		      off
      Location		      [20, 148, 476, 395]
      Open		      on
      NumInputPorts	      "2"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomI"
      SID		      "31"
      Position		      [35, 14, 65, 46]
      SampleTime	      "1e-005"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomQ"
      SID		      "32"
      Position		      [35, 74, 65, 106]
      Seed		      "1"
      SampleTime	      "1e-005"
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "Re,Im->C1"
      SID		      "48"
      Ports		      [2, 1]
      Position		      [200, 38, 230, 67]
      Input		      "Real and imag"
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "Re,Im->C2"
      SID		      "49"
      Ports		      [2, 1]
      Position		      [110, 163, 140, 192]
      Input		      "Real and imag"
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "Re,In->C3"
      SID		      "52"
      Ports		      [2, 1]
      Position		      [395, 213, 425, 242]
      BlockMirror	      on
      Input		      "Real and imag"
    }
    Block {
      BlockType		      CombinatorialLogic
      Name		      "RotDir"
      SID		      "33"
      Position		      [1040, 255, 1070, 285]
      TruthTable	      "[0;1;1;0;1;0;0;1]"
    }
    Block {
      BlockType		      Relay
      Name		      "SatC"
      SID		      "34"
      Position		      [110, 15, 140, 45]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m1"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "SatS"
      SID		      "35"
      Position		      [110, 75, 140, 105]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Select\nUp/Down"
      SID		      "36"
      Position		      [255, 545, 285, 575]
      Threshold		      "0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Counter\nInput"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "SinGen"
      SID		      "37"
      Ports		      [1, 1]
      Position		      [580, 625, 610, 655]
      Port {
	PortNumber		1
	Name			"sin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "38"
      Ports		      [2, 1]
      Position		      [315, 550, 335, 570]
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ACC\nout"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum2"
      SID		      "40"
      Ports		      [2, 1]
      Position		      [885, 166, 915, 204]
      ShowName		      off
      Inputs		      "+-"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Constant
      Name		      "UpLim"
      SID		      "41"
      Position		      [390, 590, 420, 620]
      Value		      "31"
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap<0"
      SID		      "42"
      Position		      [455, 545, 485, 575]
      Threshold		      "-0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"C_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap>31"
      SID		      "43"
      Position		      [385, 535, 415, 565]
      Threshold		      "31.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Sin
      Name		      "cos"
      SID		      "44"
      Ports		      [0, 1]
      Position		      [455, 190, 485, 220]
      BlockMirror	      on
      Frequency		      "628318.5307"
      Phase		      "pi/2"
      SampleTime	      "3.125e-007"
      Port {
	PortNumber		1
	Name			"LOcos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "cos/sin"
      SID		      "45"
      Ports		      [2]
      Position		      [710, 576, 740, 609]
      Floating		      off
      Location		      [29, 779, 353, 1018]
      Open		      off
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Gain
      Name		      "pi/16"
      SID		      "46"
      Position		      [520, 545, 550, 575]
      Gain		      "pi/16"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"phi_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "sin"
      SID		      "47"
      Ports		      [0, 1]
      Position		      [455, 265, 485, 295]
      BlockMirror	      on
      Amplitude		      "-1"
      Frequency		      "628318.5307"
      SampleTime	      "3.125e-007"
      Port {
	PortNumber		1
	Name			"LOsin"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Line {
      SrcBlock		      "RandomI"
      SrcPort		      1
      DstBlock		      "SatC"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Memory1"
      SrcPort		      1
      Points		      [-20, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "ACC\nout"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Labels			[1, 0]
	DstBlock		"Wrap>31"
	DstPort			3
      }
      Branch {
	Points			[0, -10]
	DstBlock		"Wrap>31"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "LoLim"
      SrcPort		      1
      Points		      [15, 0; 0, 50]
      DstBlock		      "Wrap>31"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Wrap>31"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Wrap<0"
	DstPort			1
      }
      Branch {
	Points			[0, 10]
	DstBlock		"Wrap<0"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "UpLim"
      SrcPort		      1
      Points		      [0, -35]
      DstBlock		      "Wrap<0"
      DstPort		      3
    }
    Line {
      Name		      "C_out"
      Labels		      [0, 0]
      SrcBlock		      "Wrap<0"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	Points			[0, -40; -210, 0]
	DstBlock		"Output"
	DstPort			4
      }
      Branch {
	DstBlock		"pi/16"
	DstPort			1
      }
      Branch {
	Points			[0, 95]
	DstBlock		"Memory1"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Inverter"
      SrcPort		      1
      Points		      [10, 0]
      DstBlock		      "Select\nUp/Down"
      DstPort		      3
    }
    Line {
      Name		      "Counter\nInput"
      Labels		      [0, 0]
      SrcBlock		      "Select\nUp/Down"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "CounterCLK"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, 40]
	DstBlock		"Inverter"
	DstPort			1
      }
      Branch {
	DstBlock		"Select\nUp/Down"
	DstPort			1
      }
    }
    Line {
      Name		      "sin"
      Labels		      [0, 0]
      SrcBlock		      "SinGen"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"GainSin"
	DstPort			1
      }
      Branch {
	Points			[0, -40]
	Branch {
	  DstBlock		  "cos/sin"
	  DstPort		  2
	}
	Branch {
	  Points		  [0, -315]
	  DstBlock		  "MulQ11"
	  DstPort		  2
	}
      }
    }
    Line {
      Name		      "cos"
      Labels		      [1, 1]
      SrcBlock		      "CosGen"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[5, 0; 0, -200]
	Branch {
	  Points		  [0, -205]
	  DstBlock		  "MulI11"
	  DstPort		  2
	}
	Branch {
	  DstBlock		  "MulQ12"
	  DstPort		  2
	}
      }
      Branch {
	Points			[0, 25]
	DstBlock		"cos/sin"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "GainSin"
      SrcPort		      1
      Points		      [5, 0]
      DstBlock		      "MulI12"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulI11"
      SrcPort		      1
      Points		      [5, 0; 0, 15]
      DstBlock		      "AddI1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulI12"
      SrcPort		      1
      Points		      [5, 0; 0, -30]
      DstBlock		      "AddI1"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulQ11"
      SrcPort		      1
      Points		      [10, 0; 0, 35]
      DstBlock		      "AddQ1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulQ12"
      SrcPort		      1
      Points		      [10, 0; 0, -25]
      DstBlock		      "AddQ1"
      DstPort		      2
    }
    Line {
      Name		      "phi_out"
      Labels		      [0, 0]
      SrcBlock		      "pi/16"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"CosGen"
	DstPort			1
      }
      Branch {
	Points			[0, 80]
	DstBlock		"SinGen"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "RandomQ"
      SrcPort		      1
      DstBlock		      "SatS"
      DstPort		      1
    }
    Line {
      Name		      "I'"
      Labels		      [0, 0]
      SrcBlock		      "AddI1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"AbsI"
	DstPort			1
      }
      Branch {
	DstBlock		"ConvI'"
	DstPort			1
      }
      Branch {
	Points			[0, -60; -535, 0; 0, 175]
	DstBlock		"Output"
	DstPort			1
      }
    }
    Line {
      Name		      "Q'"
      Labels		      [0, 0]
      SrcBlock		      "AddQ1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"AbsQ"
	DstPort			1
      }
      Branch {
	Points			[0, 60]
	Branch {
	  DstBlock		  "ConvQ'"
	  DstPort		  1
	}
	Branch {
	  Points		  [-575, 0; 0, -80]
	  DstBlock		  "Output"
	  DstPort		  2
	}
      }
    }
    Line {
      SrcBlock		      "AbsI"
      SrcPort		      1
      DstBlock		      "Sum2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Sum2"
      SrcPort		      1
      DstBlock		      "I'>Q'"
      DstPort		      1
    }
    Line {
      SrcBlock		      "AbsQ"
      SrcPort		      1
      Points		      [5, 0; 0, -130]
      DstBlock		      "Sum2"
      DstPort		      2
    }
    Line {
      Name		      "C"
      Labels		      [2, 0]
      SrcBlock		      "I'>Q'"
      SrcPort		      1
      Points		      [10, 0; 0, 60]
      DstBlock		      "Mux"
      DstPort		      1
    }
    Line {
      Name		      "Ib'"
      Labels		      [0, 0]
      SrcBlock		      "ConvI'"
      SrcPort		      1
      DstBlock		      "Mux"
      DstPort		      2
    }
    Line {
      Name		      "Qb'"
      Labels		      [0, 0]
      SrcBlock		      "ConvQ'"
      SrcPort		      1
      Points		      [65, 0; 0, -90]
      DstBlock		      "Mux"
      DstPort		      3
    }
    Line {
      SrcBlock		      "Mux"
      SrcPort		      1
      DstBlock		      "RotDir"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RotDir"
      SrcPort		      1
      Points		      [10, 0; 0, 165; -940, 0; 0, 25]
      DstBlock		      "Memory"
      DstPort		      1
    }
    Line {
      Name		      "U/D"
      Labels		      [0, 0]
      SrcBlock		      "Memory"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[0, 100]
	DstBlock		"Select\nUp/Down"
	DstPort			2
      }
      Branch {
	Points			[30, 0; 0, -140]
	DstBlock		"Output"
	DstPort			3
      }
    }
    Line {
      Name		      "m1"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "SatC"
      SrcPort		      1
      Points		      [10, 0; 0, 10]
      Branch {
	Points			[0, 5]
	DstBlock		"Re,Im->C1"
	DstPort			1
      }
      Branch {
	Points			[0, 85]
	DstBlock		"RFSignal"
	DstPort			1
      }
    }
    Line {
      Name		      "m2"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "SatS"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[0, -30]
	DstBlock		"Re,Im->C1"
	DstPort			2
      }
      Branch {
	Points			[0, 50]
	DstBlock		"RFSignal"
	DstPort			2
      }
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "CarrierI"
      SrcPort		      1
      Points		      [10, 0; 0, 15]
      DstBlock		      "Re,Im->C2"
      DstPort		      1
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "CarrierQ"
      SrcPort		      1
      Points		      [10, 0; 0, -30]
      DstBlock		      "Re,Im->C2"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Re,Im->C1"
      SrcPort		      1
      DstBlock		      "Product"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Re,Im->C2"
      SrcPort		      1
      Points		      [105, 0; 0, -110]
      DstBlock		      "Product"
      DstPort		      2
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "C->Re.Im"
      SrcPort		      1
      Points		      [125, 0; 0, 75]
      Branch {
	Points			[0, 0]
	Branch {
	  DstBlock		  "MulI11"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 130]
	  DstBlock		  "MulQ11"
	  DstPort		  1
	}
      }
      Branch {
	Points			[0, 0]
	DstBlock		"Is,Qs"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      SrcBlock		      "C->Re.Im"
      SrcPort		      2
      Points		      [60, 0; 0, 75]
      Branch {
	Points			[0, 45]
	Branch {
	  Labels		  [1, 0]
	  DstBlock		  "MulI12"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 145]
	  DstBlock		  "MulQ12"
	  DstPort		  1
	}
      }
      Branch {
	DstBlock		"Is,Qs"
	DstPort			2
      }
    }
    Line {
      Name		      "LOcos"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "cos"
      SrcPort		      1
      Points		      [-5, 0; 0, 15]
      DstBlock		      "Re,In->C3"
      DstPort		      1
    }
    Line {
      Name		      "LOsin"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "sin"
      SrcPort		      1
      Points		      [-5, 0; 0, -45]
      DstBlock		      "Re,In->C3"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Re,In->C3"
      SrcPort		      1
      Points		      [-35, 0; 0, -150]
      DstBlock		      "Product1"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Product1"
      SrcPort		      1
      DstBlock		      "C->Re.Im"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Product"
      SrcPort		      1
      DstBlock		      "Product1"
      DstPort		      1
    }
    Annotation {
      Name		      "AddI+Q"
      Position		      [276, 159]
    }
  }
}
