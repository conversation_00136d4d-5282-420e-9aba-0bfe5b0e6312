Model {
  Name			  "QPSK_Real"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.93"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model QPSK_Real\n----------------------\n\n1. Model description\n-----------------------\n\nThis is "
  "a model for QPSK communication systems. It simulates both the transmitter\nand the receiver. The receiver uses a con"
  "ventional Costas loop for carrier\nrecovery. This is in contrast to model QPSK_Comp where a \"modified\"\nCostas loo"
  "p is applied (cf. Description in model QPSK_Comp). \n\n\na. Transmitter\n----------------\nThe transmitter is built "
  "from the 11 blocks at the left of the block diagram.\nA block \"CarrierI\" generates the cosine carrier c(t) = cos(o"
  "megaC  + t) where\nomegaC is the radian carrier frequency. Another block CarrierQ generates the\nsine carrier sin(om"
  "egaC * t). Two binary random sequences m1 and m2 are created by\nblocks RandomI, SatC, RandomQ, and SatS. These rand"
  "om sequences build the\ndata signals I and Q, respectively. \nThe two rate transmission blocks RTI and RTQ are used "
  "to upsamle the sampling\nfrequency of the random sequences to the sampling frequency of the carrier \nsignals. \nThe"
  " transmitter output signal s(t) is then given by\n\n    s(t) = m1(t) * cos(omegaC * t) + m2(t) * sin(omegaC * t)\n\n"
  "b. Receiver\n-------------\nThe receiver is a conventional Costas loop for QPSK. The reconstructed carrier is a\nqua"
  "drature signal built from a sine and a cosine component (cf. output of blocks\nGain1 nand Gain2). The received signa"
  "l s(t) is multiplied with both of these carriers\nin blocks MixerI and MixerQ, respectively.\nThe output signal of t"
  "hese multipliers form the so called I and Q paths. \nBecause the output signals of MixerI and MixerQ also contain an"
  " ac signal whose\nfrequency is twice the carrier frequency, lowpass filters F1(z) and F2(z) are used\nto remove thes"
  "e ac signals. \nThe blocks LimI, MultI, LimQ, MultQ, and an adder (to the right of the diagram)\nfinally create the "
  "phase error signal ud. \nTheory shows that for small phase error ud is proportional to 2 theta_e, where theta_e\nis "
  "the phase error. The ud signal is applied to the loop filter built from block F3(z). \nThis filter is built as a PI "
  "block (proportional + integral). \nThe output signal uf of the loop filter controls the frequency omega2 of the DCO,"
  " that\nis built from blocks Product4, K0, omega0, F4(z), cos, and sin. \n\n\n2. Parameters of the model\n-----------"
  "---------------------\nA number of parameters can be specified by the operator:\n\n- fC Carrier frequency of the tra"
  "nsmitter (default = 400000 Hz)\n- fR Symbol rate (default = 100000 symb/s)\n- OS Oversampling factor used in the tra"
  "nsmitter section. The sampling frequency of\n      the model is defined as the product OS * fC. (default = 32)\n- nC"
  "ycles Number of symbols used in the simulation (default = 20)\n- delta_f Fequency error of receiver carrier frequenc"
  "y. To simulate a frequency offset,\n      the initial frequency of the DCO is set to fC - delta_f.\n\n\n3. Instructi"
  "ons for model operation\n----------------------------------------\nTo perform simulations, proceed as follows:\n\n- "
  "Load the model QPSK_Real by double-clicking the file QPSK_Real.mdl in the\n  Current Folder of Matlab\n\n- This disp"
  "lays the model and a figure window containing  5 edit controls for\n  parameter specification. When the model runs t"
  "he first time, default parameters are\n  set (cf. section 2). You now can alter these parameters. When done, click t"
  "he 'Done'\n  button. When an invalid number format has been entered in one of the edit controls,\n  an error message"
  " is issued, telling you to correct this entry and to hit the 'Done'\n  button again. Hitting this button saves the a"
  "ctual parameters to a parameter file\n  params_QPSK_Real.mat. When the model is started next, the parameters saved\n"
  "  in this files are loaded.\n  There is an option to load the initial default parameters: hit the 'Set init defaults"
  "' buttion.\n  This can be useful whenever you specified parameters that don't give useful results.\n\n- After hittin"
  "g the 'Done' button, go to the model window and start the simulation\n  (menu item Simulation/Start).;\n\n- Look at "
  "the results on the scopes InputSignal, IQOut, and ph error . \n  Here you can observe how the receiver acquires lock"
  ". Traces I and Q in scope\n  RF Signal show the data signals created by the transmitter. Traces I and Q in scope\n  "
  "IQOut show the demodulated data signals.\n\n- From the waveforms in scope ph error the pull-in time can be estimated"
  ".\n  Increase e.g. the frequency error delta_f, e.g. to 50000Hz and repeat\n  the simulation. Now you will see that "
  "the loop needs more time to lock.\n\n\n4. Comment on the pull-in range of the Costas loop\n-------------------------"
  "----------------------------------\nA conventional PLL that uses a loop filter with an integrator (PI filter) is\nex"
  "pected to show an \"infinite\" pull-in range delta_f_p. In practice, the pull-in\nrange is limited by the frequency "
  "range the DCO (or VCO) is able to\ngenerate. The situation is different, however,  with this type  of Costas\nloop. "
  "When lock has notyet been acquired, theory shows that the error\nsignal ud created by the phase detector contains a "
  "dc component that\nis inversely proportional to the frequency error (difference between \ntransmitter and receiver c"
  "arrier frequencies). But theory says furthermore\nthat this dc term is multiplied by a cosine term cos(2*ph1 + phi3)"
  ", where\nphi1 is the phase shift of the lowpass filter F1(z) and phi3 is the phase\nshift of the loop filter F3(z). "
  "(The phase shift of F2(z) is identical with the\nphase shift of F1(z). When the frequency error is large, the sum 2*"
  "phi1\n+ phi3 exceeds 90 degrees, hence the cosine term becomes negative.\nThis implies that the dc component of the "
  "ud signal changes polarity, and\nthe frequency control of the loop operates in the \"wrong\" direction.\nSimulations"
  " with different values of parameter delta_f show that the\npull-in range of this Costas loop is somewhat more than 5"
  "0 kHz. (Theory\npredicts a pull-in range in the order of 40 kHz.)\n\n\n5. Comment on the \"phase ambiguity\" of the "
  "Costas loop\n-----------------------------------------------------------------\nWhen performing simulations with dif"
  "ferent values of frequency error\ndelta_f you will recognize in some situations that the polarity of the received si"
  "gnals\ncan be inverted, or the I signal can be exchanged with the Q signal.\nThis occurs because the Costas looop fo"
  "r BQPSK can lock with 4 possible \nphase differences between transmitter and receiver carriers, i. e. with a \nphase"
  " difference of 0, 90, 180, or 270 degrees.\nTo avoid this ambiguity additional measures have to been taken. A common"
  "\nmethod is to use a given preamble at each start of a data transmission,\ne.g. a sequence of all 1's or all 0's or "
  "another predifined pattern.\nBecause the receiver \"knows\" that preamble it will use the preamble \npattern in plac"
  "e of the demodulated I signal at start of every data \ntransmission. This method is demonstrated in an other model ("
  "BPSK_Real_PreAmb).\nThe same procedure could be applied in this model, i.e. we would have to \ncreate a predefined p"
  "reamble for both I and Q signals, e.g. a sequence of all 1's.\n\n\n"
  SavedCharacterEncoding  "windows-1252"
  PreLoadFcn		  "PreLoadFcnQPSK_Real"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  CloseFcn		  "CloseFcnQPSK_Real"
  InitFcn		  "InitFcnQPSK_Real"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Thu Jan 05 15:44:47 2017"
  RTWModifiedTimeStamp	  266172672
  ModelVersionFormat	  "1.%<AutoIncrement:93>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "7.81e-008"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "FixedStepDiscrete"
	  SolverName		  "FixedStepDiscrete"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  on
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "warning"
	  UnconnectedOutputMsg	  "warning"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Solver"
      ConfigPrmDlgPosition    " [ 280, 135, 1160, 765 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Numerator		      "[1]"
      Denominator	      "[1 0.5]"
      InitialStates	      "0"
      SampleTime	      "1"
      a0EqualsOne	      off
      NumCoefMin	      "[]"
      NumCoefMax	      "[]"
      DenCoefMin	      "[]"
      DenCoefMax	      "[]"
      OutMin		      "[]"
      OutMax		      "[]"
      StateDataTypeStr	      "Inherit: Same as input"
      NumCoefDataTypeStr      "Inherit: Inherit via internal rule"
      DenCoefDataTypeStr      "Inherit: Inherit via internal rule"
      NumProductDataTypeStr   "Inherit: Inherit via internal rule"
      DenProductDataTypeStr   "Inherit: Inherit via internal rule"
      NumAccumDataTypeStr     "Inherit: Inherit via internal rule"
      DenAccumDataTypeStr     "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParamMin		      "[]"
      ParamMax		      "[]"
      ParamDataTypeStr	      "Inherit: Same as input"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RateTransition
      Integrity		      on
      Deterministic	      on
      X0		      "0"
      OutPortSampleTimeOpt    "Specify"
      OutPortSampleTimeMultiple	"1"
      OutPortSampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Trigonometry
      Operator		      "sin"
      ApproximationMethod     "None"
      NumberOfIterations      "11"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "QPSK_Real"
    Location		    [123, 285, 1180, 784]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "41"
    Block {
      BlockType		      Sin
      Name		      "CarrierI"
      SID		      "1"
      Ports		      [0, 1]
      Position		      [15, 75, 45, 105]
      Frequency		      "2513274.1229"
      Phase		      "pi/2"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "CarrierQ"
      SID		      "2"
      Ports		      [0, 1]
      Position		      [15, 255, 45, 285]
      Frequency		      "2513274.1229"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F1(z)"
      SID		      "3"
      Ports		      [1, 1]
      Position		      [525, 92, 585, 128]
      Numerator		      "[1  1]"
      Denominator	      "[21.3555     -19.3555]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F2(z)"
      SID		      "4"
      Ports		      [1, 1]
      Position		      [535, 297, 595, 333]
      Numerator		      "[1  1]"
      Denominator	      "[21.3555     -19.3555]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F3(z)"
      SID		      "5"
      Ports		      [1, 1]
      Position		      [835, 172, 895, 208]
      BlockMirror	      on
      Numerator		      "[102.8559     -100.8559]"
      Denominator	      "[512 -512]"
      SampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"uf(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      DiscreteTransferFcn
      Name		      "F4(z)"
      SID		      "6"
      Ports		      [1, 1]
      Position		      [620, 182, 680, 218]
      BlockMirror	      on
      Numerator		      "[0 7.8125e-008]"
      Denominator	      "[1 -1]"
      SampleTime	      "7.81e-008"
    }
    Block {
      BlockType		      Gain
      Name		      "Gain"
      SID		      "7"
      Position		      [760, 35, 790, 65]
      Gain		      "1/(2*pi)"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"f2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Gain1"
      SID		      "39"
      Position		      [475, 155, 505, 185]
      BlockMirror	      on
      Gain		      "2"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Gain
      Name		      "Gain2"
      SID		      "40"
      Position		      [475, 230, 505, 260]
      BlockMirror	      on
      Gain		      "2"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "IQOut"
      SID		      "8"
      Ports		      [2]
      Position		      [765, 356, 795, 389]
      Floating		      off
      Location		      [19, 719, 523, 958]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      YMin		      "-1.5~-1.5"
      YMax		      "1.5~1.5"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "InputSignal"
      SID		      "9"
      Ports		      [3]
      Position		      [345, 268, 375, 312]
      Floating		      off
      Location		      [14, 412, 517, 677]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-1.5"
      YMax		      "1.1~1.1~1.5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "K0"
      SID		      "10"
      Position		      [845, 245, 875, 275]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "631654.6817"
    }
    Block {
      BlockType		      Relay
      Name		      "LimI"
      SID		      "12"
      Position		      [760, 95, 790, 125]
      OffOutputValue	      "-1"
    }
    Block {
      BlockType		      Relay
      Name		      "LimQ"
      SID		      "13"
      Position		      [765, 300, 795, 330]
      OffOutputValue	      "-1"
    }
    Block {
      BlockType		      Product
      Name		      "MixerI"
      SID		      "14"
      Ports		      [2, 1]
      Position		      [460, 92, 490, 123]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MixerQ"
      SID		      "15"
      Ports		      [2, 1]
      Position		      [465, 297, 495, 328]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "ModulC"
      SID		      "16"
      Ports		      [2, 1]
      Position		      [250, 82, 280, 113]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"c(t)m(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "ModulS"
      SID		      "17"
      Ports		      [2, 1]
      Position		      [250, 207, 280, 238]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s(t)m2(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "MultI"
      SID		      "20"
      Ports		      [2, 1]
      Position		      [840, 102, 870, 133]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"NI(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "MultQ"
      SID		      "21"
      Ports		      [2, 1]
      Position		      [845, 292, 875, 323]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"NQ(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Product
      Name		      "Product4"
      SID		      "22"
      Ports		      [2, 1]
      Position		      [780, 182, 810, 213]
      BlockMirror	      on
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTI"
      SID		      "35"
      Position		      [160, 124, 200, 166]
      OutPortSampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"I"
	PropagatedSignals	"m1(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      RateTransition
      Name		      "RTQ"
      SID		      "36"
      Position		      [160, 194, 200, 236]
      OutPortSampleTime	      "7.81e-008"
      Port {
	PortNumber		1
	Name			"Q"
	PropagatedSignals	"m2(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomI"
      SID		      "23"
      Position		      [15, 129, 45, 161]
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "RandomQ"
      SID		      "24"
      Position		      [15, 199, 45, 231]
      Seed		      "1"
      SampleTime	      "9.9968e-006"
    }
    Block {
      BlockType		      Relay
      Name		      "SatC"
      SID		      "25"
      Position		      [80, 130, 110, 160]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m1(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "SatS"
      SID		      "26"
      Position		      [80, 200, 110, 230]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m2(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "27"
      Ports		      [2, 1]
      Position		      [740, 190, 760, 210]
      BlockMirror	      on
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"omega2"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum1"
      SID		      "28"
      Ports		      [2, 1]
      Position		      [350, 90, 370, 110]
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "Sum2"
      SID		      "29"
      Ports		      [2, 1]
      Position		      [930, 170, 965, 205]
      BlockMirror	      on
      ShowName		      off
      Inputs		      "-+"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ud(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "cos"
      SID		      "30"
      Ports		      [1, 1]
      Position		      [525, 155, 555, 185]
      BlockMirror	      on
      Operator		      "cos"
    }
    Block {
      BlockType		      Constant
      Name		      "omega0"
      SID		      "32"
      Position		      [775, 245, 805, 275]
      BlockMirror	      on
      NamePlacement	      "alternate"
      Value		      "2261946.7106"
    }
    Block {
      BlockType		      Scope
      Name		      "ph error"
      SID		      "33"
      Ports		      [3]
      Position		      [955, 23, 985, 57]
      Floating		      off
      Location		      [17, 57, 520, 348]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-2~-1~200000"
      YMax		      "2~1~600000"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Trigonometry
      Name		      "sin"
      SID		      "34"
      Ports		      [1, 1]
      Position		      [525, 230, 555, 260]
      BlockMirror	      on
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0]
      SrcBlock		      "CarrierI"
      SrcPort		      1
      DstBlock		      "ModulC"
      DstPort		      1
    }
    Line {
      Name		      "m1(t)"
      Labels		      [0, 0]
      SrcBlock		      "SatC"
      SrcPort		      1
      DstBlock		      "RTI"
      DstPort		      1
    }
    Line {
      Labels		      [0, 0]
      SrcBlock		      "MixerI"
      SrcPort		      1
      DstBlock		      "F1(z)"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MixerQ"
      SrcPort		      1
      DstBlock		      "F2(z)"
      DstPort		      1
    }
    Line {
      Name		      "uf(t)"
      Labels		      [0, 0]
      SrcBlock		      "F3(z)"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	DstBlock		"Product4"
	DstPort			1
      }
      Branch {
	Points			[0, -150]
	DstBlock		"ph error"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "K0"
      SrcPort		      1
      Points		      [-15, 0]
      DstBlock		      "Product4"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Product4"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "omega0"
      SrcPort		      1
      Points		      [-20, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "omega2"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [-5, 0]
      Branch {
	Points			[0, -150]
	DstBlock		"Gain"
	DstPort			1
      }
      Branch {
	DstBlock		"F4(z)"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "RandomI"
      SrcPort		      1
      DstBlock		      "SatC"
      DstPort		      1
    }
    Line {
      SrcBlock		      "RandomQ"
      SrcPort		      1
      DstBlock		      "SatS"
      DstPort		      1
    }
    Line {
      Name		      "m2(t)"
      Labels		      [0, 0]
      SrcBlock		      "SatS"
      SrcPort		      1
      DstBlock		      "RTQ"
      DstPort		      1
    }
    Line {
      Name		      "c(t)m(t)"
      Labels		      [0, 0]
      SrcBlock		      "ModulC"
      SrcPort		      1
      DstBlock		      "Sum1"
      DstPort		      1
    }
    Line {
      Name		      "s(t)m2(t)"
      Labels		      [0, 0]
      SrcBlock		      "ModulS"
      SrcPort		      1
      Points		      [75, 0]
      DstBlock		      "Sum1"
      DstPort		      2
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0]
      SrcBlock		      "Sum1"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[20, 0]
	Branch {
	  DstBlock		  "MixerI"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 220]
	  DstBlock		  "MixerQ"
	  DstPort		  2
	}
      }
      Branch {
	Points			[0, 240; -85, 0; 0, -35]
	DstBlock		"InputSignal"
	DstPort			3
      }
    }
    Line {
      Name		      "I"
      Labels		      [0, 0]
      SrcBlock		      "F1(z)"
      SrcPort		      1
      Points		      [110, 0]
      Branch {
	DstBlock		"LimI"
	DstPort			1
      }
      Branch {
	Points			[0, 180]
	Branch {
	  Points		  [130, 0]
	  DstBlock		  "MultQ"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 75]
	  DstBlock		  "IQOut"
	  DstPort		  1
	}
      }
    }
    Line {
      Name		      "Q"
      Labels		      [0, 0]
      SrcBlock		      "F2(z)"
      SrcPort		      1
      Points		      [115, 0]
      Branch {
	DstBlock		"LimQ"
	DstPort			1
      }
      Branch {
	Points			[0, -170; 90, 0; 0, -20]
	DstBlock		"MultI"
	DstPort			2
      }
      Branch {
	Points			[0, 65]
	DstBlock		"IQOut"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "LimI"
      SrcPort		      1
      DstBlock		      "MultI"
      DstPort		      1
    }
    Line {
      SrcBlock		      "LimQ"
      SrcPort		      1
      DstBlock		      "MultQ"
      DstPort		      2
    }
    Line {
      Name		      "ud(t)"
      Labels		      [0, 0]
      SrcBlock		      "Sum2"
      SrcPort		      1
      Points		      [-10, 0]
      Branch {
	Labels			[2, 0]
	Points			[0, -160]
	DstBlock		"ph error"
	DstPort			1
      }
      Branch {
	DstBlock		"F3(z)"
	DstPort			1
      }
    }
    Line {
      Name		      "NI(t)"
      Labels		      [0, 0]
      SrcBlock		      "MultI"
      SrcPort		      1
      Points		      [115, 0; 0, 60]
      DstBlock		      "Sum2"
      DstPort		      1
    }
    Line {
      Name		      "NQ(t)"
      Labels		      [0, 0]
      SrcBlock		      "MultQ"
      SrcPort		      1
      Points		      [110, 0; 0, -115]
      DstBlock		      "Sum2"
      DstPort		      2
    }
    Line {
      Name		      "f2"
      Labels		      [0, 0]
      SrcBlock		      "Gain"
      SrcPort		      1
      DstBlock		      "ph error"
      DstPort		      3
    }
    Line {
      Name		      "I"
      SrcBlock		      "RTI"
      SrcPort		      1
      Points		      [15, 0]
      Branch {
	Points			[0, -40]
	DstBlock		"ModulC"
	DstPort			2
      }
      Branch {
	Labels			[2, 0]
	Points			[0, 130]
	DstBlock		"InputSignal"
	DstPort			1
      }
    }
    Line {
      Name		      "Q"
      SrcBlock		      "RTQ"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	DstBlock		"ModulS"
	DstPort			1
      }
      Branch {
	Labels			[2, 0]
	Points			[0, 75]
	DstBlock		"InputSignal"
	DstPort			2
      }
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0; 0, 0]
      SrcBlock		      "CarrierQ"
      SrcPort		      1
      Points		      [160, 0; 0, -40]
      DstBlock		      "ModulS"
      DstPort		      2
    }
    Line {
      SrcBlock		      "F4(z)"
      SrcPort		      1
      Points		      [-20, 0]
      Branch {
	Points			[0, -30]
	DstBlock		"cos"
	DstPort			1
      }
      Branch {
	Points			[0, 45]
	DstBlock		"sin"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "cos"
      SrcPort		      1
      DstBlock		      "Gain1"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Gain1"
      SrcPort		      1
      Points		      [-35, 0; 0, -55]
      DstBlock		      "MixerI"
      DstPort		      2
    }
    Line {
      SrcBlock		      "sin"
      SrcPort		      1
      DstBlock		      "Gain2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Gain2"
      SrcPort		      1
      Points		      [-30, 0; 0, 60]
      DstBlock		      "MixerQ"
      DstPort		      1
    }
    Annotation {
      Name		      "Transmitter"
      Position		      [98, 45]
    }
    Annotation {
      Name		      "Receiver"
      Position		      [513, 53]
    }
  }
}
