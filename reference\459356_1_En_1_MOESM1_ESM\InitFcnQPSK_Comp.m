% Init Function of Model QPSK_Comp
% This function is loaded whenever a simulation is started
% It loads the parameters of the simulation (as specified in the preceding
% PreLoadFcnQPSK_Comp.m) into the model


% Compute the parameters of the model

Kd = 1;
fS = OS * fC;  % Define the sampling frequency of the (discrete) model
T = 1/fS;      % sampling interal of the model
TD = D * T;    % sampling interval for Costas loop (decimated)
fT = 0.4 * fR; % Transit frequency of PLL (Costas loop)
omegaT = 2 * pi * fT;
tau1 = 20e-6;  % integrator time constant in PI loop filter 
K0 = tau1 * omegaT^2/Kd; % VCO gain
omega0 = 2 * pi * (fC - delta_f);
tR = 1/fR; % duration of one symbol (bit)
omegaC = 2 * pi * fC;

% Check the ratio of sampling times of the model. This ratio must be
% an integer number. If not, round sampling times to fulfill that
% requirement.

T_string = num2str(T, 3);
% Note: T_string is string variable of sampling time T (rounded)
T_round = str2num(T_string);
tR_string = num2str(tR, 3);
tR_round = str2num(tR_string);
K = round(tR_round/T_round); % round ratio of sampling times to integer
tR_string = num2str(K*T_round, 9);
% Note: tR_string is string variable of sampling time tR (rounded)
TD = D * T_round;
TD_string = num2str(TD, 6);
% Note: TD_string is string variable of sampling time TD (rounded)

% prewarp corner frequencies (for bilinear z transform)
omegaT_P = (2/T) * tan(omegaT * T/2);

% enter parameters into the model

set_param('QPSK_Comp', 'FixedStep', T_string); % sampling time of model
set_param('QPSK_Comp', 'Stop time', num2str(nCycles * tR)); 

set_param('QPSK_Comp/RTI', 'OutPortSampleTime', T_string);
set_param('QPSK_Comp/RTQ', 'OutPortSampleTime', T_string);
set_param('QPSK_Comp/RTRot', 'OutPortSampleTime', TD_string);

set_param('QPSK_Comp/CarrierI', 'Sample time', T_string);
set_param('QPSK_Comp/CarrierI', 'Frequency', num2str(omegaC));
set_param('QPSK_Comp/CarrierQ', 'Sample time', T_string);
set_param('QPSK_Comp/CarrierQ', 'Frequency', num2str(omegaC));

set_param('QPSK_Comp/RandomI', 'Sample time', tR_string);
set_param('QPSK_Comp/RandomQ', 'Sample time', tR_string);

set_param('QPSK_Comp/Hilbert', 'NumDelays', num2str(OS/4));

set_param('QPSK_Comp/F3(z)', 'Numerator', ['[', num2str([1+2/(omegaT_P*TD), 1-2/(omegaT_P*TD)]), ']']); 
set_param('QPSK_Comp/F3(z)', 'Denominator', ['[', num2str([2*tau1/TD, -2*tau1/TD]), ']']);
set_param('QPSK_Comp/F3(z)', 'Sample time', TD_string);

set_param('QPSK_Comp/F4(z)', 'Numerator', ['[', num2str([0, TD]), ']']);
set_param('QPSK_Comp/F4(z)', 'Denominator', ['[', num2str([1, -1]), ']']);
set_param('QPSK_Comp/F4(z)', 'Sample time', TD_string);

set_param('QPSK_Comp/K0', 'Value', num2str(K0));

set_param('QPSK_Comp/omega0', 'Value', num2str(omega0));
