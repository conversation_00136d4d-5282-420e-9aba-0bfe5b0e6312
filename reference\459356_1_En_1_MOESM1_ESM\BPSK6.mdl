Model {
  Name			  "BPSK6"
  Version		  7.6
  MdlSubVersion		  0
  GraphicalInterface {
    NumRootInports	    0
    NumRootOutports	    0
    ParameterArgumentNames  ""
    ComputedModelVersion    "1.116"
    NumModelReferences	    0
    NumTestPointedSignals   0
  }
  Description		  "Model BPSK6\n----------------------\n\nThis model is similar to model BPSK5, but is a \"modified\" C"
  "ostas loop; it operates with\na pre-envelope signal. The transmitter (shown on top left) first creates a sine s(t) a"
  "nd a\ncosine c(t) signal. The carrier frequency is fC. These signals are combined into a complex \ncarrier c(t) + j "
  "s(t) by block Re,Im->C. The complex carrier is multiplied with the \nrandom binary signal m(t) by the multiplier lab"
  "elled \"Product1\". The output signal \nof that multiplier is the pre-envelope signal s+(t).\n\nThe local quadrature"
  " oscillator built from the blocks sin and cos create a \n\"wrong\" local frequency f0 that is purposedly offset from"
  " the carrier frequency\nMixerI and MixerQ are used to create the demodulated signal I, Q. These 2 signals \nare \"ro"
  "tated\" by a frequency that equals the frequency\noffset fC - f0.\n\nNow a vector rotator is used to change the phas"
  "e of vector (I, Q) such that the \nrotated vector (I', Q') has either phase 0 or 180 degrees. Doing so sets the Q' \n"
  "component to nearly 0, and I' is the required demodulated BPSK signal. \nThe rotator is shown on bottom of the block"
  " diagram. An up/down counter is used\nto rotate the I,Q vector clockwise or counterclockwise. The analysis shows tha"
  "t\nthe vector must be rotated positively when the EXOR function of the quantized\nI', Q' vector is TRUE. This is sho"
  "wn in the center of the diagram. Relay1 and Relay2\nare used to quantize the I',Q' vector. \n\nThe output signal of "
  "the EXOR controls the counting direction (Select Up/Down).\nThe counter is built from an accumulator consisting of a"
  "n adder block entitled\nCounterACC. The counter must wrap to zero when it counts forward and its content\nwould beco"
  "me larger than 15. In analogy, the counter must wrap to 15 when it \ncounts down and its content would get less than"
  " 0. The counter output is labeled\nC_out. The counter output is multiplied by the phase increment pi/8 (22.5 degrees"
  ").\nThe blocks labeled CosGen and SinGen compute sine and cosine of the\naccumated phase, i.e. from C_out * pi/8. \n"
  "\nNow the vector I,Q is rotated by computing\n\n          I' = I * cos(phi_acc) - Q * sin(phi_acc)\n          Q' = I"
  " * sin(phi_acc) + Q * cos(phi_acc)\n\nwhere phi_acc is the accumulated phase. \n\nNote that the I'_del signal is a q"
  "uantized version of the output signal I'. I'_del is\nconsequently the desired demodulated BPSK signal.\n\nNote1: the"
  " delay blocks DelayI and DelayQ had to be inserted to avoid an\nalgebraic loop. \n\nNote2: The vector rotator is clo"
  "cked with a sampling frequency equal to 4 times\nthe carrier frequency fC, default is 1.6 MHz.\n\nNote3: When the Co"
  "stas loop has settled, the content of the Up/Down counter \ntoggles between the adjacent values. e.g. 5 and 6, respe"
  "ctively. Doing so the rotated\nI',Q' vector toggles swings around the horizontal axis, with I' nearly 1 or -1, and w"
  "ith\nQ' nearly 0. \n\nPull-in range of this Costas loop\n------------------------------------\nThe pull-in range is "
  "given by the maximum rotation speed of the phasor I, Q. \nAssume that the model works with the default values of\n\n"
  "    fC = 400 kHz (carrier frequency)\n    fR = 100'000 bits/s\n\nThe sampling frequency of the following block C->Re"
  ",Im (top right) operate with\na sampling frequency of 12.8 MHz (32 times the carrier frequency). Consequently the\no"
  "versampling factor OS for the phasor rotator is 12.8MHz/400 kHz = 128. The phase step\ndelta_phi has been chosen 2*p"
  "i/16, hence the pull-in frequency delta_f_P becomes \n800 kHz. \n\nSimulations confirm the predicted pull-in frequen"
  "cy.\nWhen you have chosen a carrier frequency of 400 kHz e.g., you can enter a \nfrequency error up to +400 kHz, but"
  " not values exceeding 400 kHz, because \nthis would lead to a negative frequency of the local oscillator, and this i"
  "s not\nsupported by Simulink. But it is possible to specify negative values for the \nfrequency error down to - 800 "
  "kHz. Specifying a frequency error of \n- 800 kHz e.g. leads to a frequency of the local oscillator of 1.2 MHz, which"
  " \nis allowed."
  SavedCharacterEncoding  "windows-1252"
  SaveDefaultBlockParams  on
  ScopeRefreshTime	  0.035000
  OverrideScopeRefreshTime on
  DisableAllScopes	  off
  DataTypeOverride	  "UseLocalSettings"
  DataTypeOverrideAppliesTo "AllNumericTypes"
  MinMaxOverflowLogging	  "UseLocalSettings"
  MinMaxOverflowArchiveMode "Overwrite"
  MaxMDLFileLineLength	  120
  InitFcn		  "StartFunctionBPSK6"
  Created		  "Fri Jul 06 15:04:21 2007"
  Creator		  "Administrator"
  UpdateHistory		  "UpdateHistoryNever"
  ModifiedByFormat	  "%<Auto>"
  LastModifiedBy	  "Administrator"
  ModifiedDateFormat	  "%<Auto>"
  LastModifiedDate	  "Thu Sep 14 15:06:19 2017"
  RTWModifiedTimeStamp	  407691117
  ModelVersionFormat	  "1.%<AutoIncrement:116>"
  ConfigurationManager	  "None"
  SampleTimeColors	  off
  SampleTimeAnnotations	  off
  LibraryLinkDisplay	  "none"
  WideLines		  off
  ShowLineDimensions	  off
  ShowPortDataTypes	  off
  ShowLoopsOnError	  on
  IgnoreBidirectionalLines off
  ShowStorageClass	  off
  ShowTestPointIcons	  on
  ShowSignalResolutionIcons on
  ShowViewerIcons	  on
  SortedOrder		  off
  ExecutionContextIcon	  off
  ShowLinearizationAnnotations on
  BlockNameDataTip	  off
  BlockParametersDataTip  off
  BlockDescriptionStringDataTip	off
  ToolBar		  on
  StatusBar		  on
  BrowserShowLibraryLinks off
  BrowserLookUnderMasks	  off
  SimulationMode	  "normal"
  LinearizationMsg	  "none"
  Profile		  off
  ParamWorkspaceSource	  "MATLABWorkspace"
  AccelSystemTargetFile	  "accel.tlc"
  AccelTemplateMakefile	  "accel_default_tmf"
  AccelMakeCommand	  "make_rtw"
  TryForcingSFcnDF	  off
  RecordCoverage	  off
  CovPath		  "/"
  CovSaveName		  "covdata"
  CovMetricSettings	  "dw"
  CovNameIncrementing	  off
  CovHtmlReporting	  on
  CovForceBlockReductionOff on
  covSaveCumulativeToWorkspaceVar on
  CovSaveSingleToWorkspaceVar on
  CovCumulativeVarName	  "covCumulativeData"
  CovCumulativeReport	  off
  CovReportOnPause	  on
  CovModelRefEnable	  "Off"
  CovExternalEMLEnable	  off
  ExtModeBatchMode	  off
  ExtModeEnableFloating	  on
  ExtModeTrigType	  "manual"
  ExtModeTrigMode	  "normal"
  ExtModeTrigPort	  "1"
  ExtModeTrigElement	  "any"
  ExtModeTrigDuration	  1000
  ExtModeTrigDurationFloating "auto"
  ExtModeTrigHoldOff	  0
  ExtModeTrigDelay	  0
  ExtModeTrigDirection	  "rising"
  ExtModeTrigLevel	  0
  ExtModeArchiveMode	  "off"
  ExtModeAutoIncOneShot	  off
  ExtModeIncDirWhenArm	  off
  ExtModeAddSuffixToVar	  off
  ExtModeWriteAllDataToWs off
  ExtModeArmWhenConnect	  on
  ExtModeSkipDownloadWhenConnect off
  ExtModeLogAll		  on
  ExtModeAutoUpdateStatusClock on
  BufferReuse		  on
  ShowModelReferenceBlockVersion off
  ShowModelReferenceBlockIO off
  Array {
    Type		    "Handle"
    Dimension		    1
    Simulink.ConfigSet {
      $ObjectID		      1
      Version		      "1.10.0"
      Array {
	Type			"Handle"
	Dimension		8
	Simulink.SolverCC {
	  $ObjectID		  2
	  Version		  "1.10.0"
	  StartTime		  "0.0"
	  StopTime		  "0.0002"
	  AbsTol		  "auto"
	  FixedStep		  "auto"
	  InitialStep		  "auto"
	  MaxNumMinSteps	  "-1"
	  MaxOrder		  5
	  ZcThreshold		  "auto"
	  ConsecutiveZCsStepRelTol "10*128*eps"
	  MaxConsecutiveZCs	  "1000"
	  ExtrapolationOrder	  4
	  NumberNewtonIterations  1
	  MaxStep		  "auto"
	  MinStep		  "auto"
	  MaxConsecutiveMinStep	  "1"
	  RelTol		  "1e-3"
	  SolverMode		  "Auto"
	  ConcurrentTasks	  off
	  Solver		  "ode45"
	  SolverName		  "ode45"
	  SolverJacobianMethodControl "auto"
	  ShapePreserveControl	  "DisableAll"
	  ZeroCrossControl	  "UseLocalSettings"
	  ZeroCrossAlgorithm	  "Nonadaptive"
	  AlgebraicLoopSolver	  "TrustRegion"
	  SolverResetMethod	  "Fast"
	  PositivePriorityOrder	  off
	  AutoInsertRateTranBlk	  off
	  SampleTimeConstraint	  "Unconstrained"
	  InsertRTBMode		  "Whenever possible"
	}
	Simulink.DataIOCC {
	  $ObjectID		  3
	  Version		  "1.10.0"
	  Decimation		  "1"
	  ExternalInput		  "[t, u]"
	  FinalStateName	  "xFinal"
	  InitialState		  "xInitial"
	  LimitDataPoints	  on
	  MaxDataPoints		  "1000"
	  LoadExternalInput	  off
	  LoadInitialState	  off
	  SaveFinalState	  off
	  SaveCompleteFinalSimState off
	  SaveFormat		  "Array"
	  SaveOutput		  on
	  SaveState		  off
	  SignalLogging		  on
	  DSMLogging		  on
	  InspectSignalLogs	  off
	  SaveTime		  on
	  ReturnWorkspaceOutputs  off
	  StateSaveName		  "xout"
	  TimeSaveName		  "tout"
	  OutputSaveName	  "yout"
	  SignalLoggingName	  "logsout"
	  DSMLoggingName	  "dsmout"
	  OutputOption		  "RefineOutputTimes"
	  OutputTimes		  "[]"
	  ReturnWorkspaceOutputsName "out"
	  Refine		  "1"
	}
	Simulink.OptimizationCC {
	  $ObjectID		  4
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    4
	    Cell		    "ZeroExternalMemoryAtStartup"
	    Cell		    "ZeroInternalMemoryAtStartup"
	    Cell		    "NoFixptDivByZeroProtection"
	    Cell		    "OptimizeModelRefInitCode"
	    PropName		    "DisabledProps"
	  }
	  BlockReduction	  on
	  BooleanDataType	  off
	  ConditionallyExecuteInputs on
	  InlineParams		  off
	  UseIntDivNetSlope	  off
	  UseSpecifiedMinMax	  off
	  InlineInvariantSignals  off
	  OptimizeBlockIOStorage  on
	  BufferReuse		  on
	  EnhancedBackFolding	  off
	  StrengthReduction	  off
	  ExpressionFolding	  on
	  BooleansAsBitfields	  off
	  BitfieldContainerType	  "uint_T"
	  EnableMemcpy		  on
	  MemcpyThreshold	  64
	  PassReuseOutputArgsAs	  "Structure reference"
	  ExpressionDepthLimit	  2147483647
	  FoldNonRolledExpr	  on
	  LocalBlockOutputs	  on
	  RollThreshold		  5
	  SystemCodeInlineAuto	  off
	  StateBitsets		  off
	  DataBitsets		  off
	  UseTempVars		  off
	  ZeroExternalMemoryAtStartup on
	  ZeroInternalMemoryAtStartup on
	  InitFltsAndDblsToZero	  on
	  NoFixptDivByZeroProtection off
	  EfficientFloat2IntCast  off
	  EfficientMapNaN2IntZero on
	  OptimizeModelRefInitCode off
	  LifeSpan		  "inf"
	  MaxStackSize		  "Inherit from target"
	  BufferReusableBoundary  on
	  SimCompilerOptimization "Off"
	  AccelVerboseBuild	  off
	}
	Simulink.DebuggingCC {
	  $ObjectID		  5
	  Version		  "1.10.0"
	  RTPrefix		  "error"
	  ConsistencyChecking	  "none"
	  ArrayBoundsChecking	  "none"
	  SignalInfNanChecking	  "none"
	  SignalRangeChecking	  "none"
	  ReadBeforeWriteMsg	  "UseLocalSettings"
	  WriteAfterWriteMsg	  "UseLocalSettings"
	  WriteAfterReadMsg	  "UseLocalSettings"
	  AlgebraicLoopMsg	  "warning"
	  ArtificialAlgebraicLoopMsg "warning"
	  SaveWithDisabledLinksMsg "warning"
	  SaveWithParameterizedLinksMsg	"none"
	  CheckSSInitialOutputMsg on
	  UnderspecifiedInitializationDetection	"Classic"
	  MergeDetectMultiDrivingBlocksExec "none"
	  CheckExecutionContextPreStartOutputMsg off
	  CheckExecutionContextRuntimeOutputMsg	off
	  SignalResolutionControl "TryResolveAllWithWarning"
	  BlockPriorityViolationMsg "warning"
	  MinStepSizeMsg	  "warning"
	  TimeAdjustmentMsg	  "none"
	  MaxConsecutiveZCsMsg	  "error"
	  MaskedZcDiagnostic	  "warning"
	  IgnoredZcDiagnostic	  "warning"
	  SolverPrmCheckMsg	  "none"
	  InheritedTsInSrcMsg	  "warning"
	  DiscreteInheritContinuousMsg "warning"
	  MultiTaskDSMMsg	  "error"
	  MultiTaskCondExecSysMsg "error"
	  MultiTaskRateTransMsg	  "error"
	  SingleTaskRateTransMsg  "none"
	  TasksWithSamePriorityMsg "warning"
	  SigSpecEnsureSampleTimeMsg "warning"
	  CheckMatrixSingularityMsg "none"
	  IntegerOverflowMsg	  "warning"
	  Int32ToFloatConvMsg	  "warning"
	  ParameterDowncastMsg	  "error"
	  ParameterOverflowMsg	  "error"
	  ParameterUnderflowMsg	  "none"
	  ParameterPrecisionLossMsg "warning"
	  ParameterTunabilityLossMsg "warning"
	  FixptConstUnderflowMsg  "none"
	  FixptConstOverflowMsg	  "none"
	  FixptConstPrecisionLossMsg "none"
	  UnderSpecifiedDataTypeMsg "none"
	  UnnecessaryDatatypeConvMsg "none"
	  VectorMatrixConversionMsg "none"
	  InvalidFcnCallConnMsg	  "error"
	  FcnCallInpInsideContextMsg "Use local settings"
	  SignalLabelMismatchMsg  "none"
	  UnconnectedInputMsg	  "warning"
	  UnconnectedOutputMsg	  "warning"
	  UnconnectedLineMsg	  "warning"
	  SFcnCompatibilityMsg	  "none"
	  UniqueDataStoreMsg	  "none"
	  BusObjectLabelMismatch  "warning"
	  RootOutportRequireBusObject "warning"
	  AssertControl		  "UseLocalSettings"
	  EnableOverflowDetection off
	  ModelReferenceIOMsg	  "none"
	  ModelReferenceMultiInstanceNormalModeStructChecksumCheck "error"
	  ModelReferenceVersionMismatchMessage "none"
	  ModelReferenceIOMismatchMessage "none"
	  ModelReferenceCSMismatchMessage "none"
	  UnknownTsInhSupMsg	  "warning"
	  ModelReferenceDataLoggingMessage "warning"
	  ModelReferenceSymbolNameMessage "warning"
	  ModelReferenceExtraNoncontSigs "error"
	  StateNameClashWarn	  "warning"
	  SimStateInterfaceChecksumMismatchMsg "warning"
	  InitInArrayFormatMsg	  "warning"
	  StrictBusMsg		  "Warning"
	  BusNameAdapt		  "WarnAndRepair"
	  NonBusSignalsTreatedAsBus "none"
	  LoggingUnavailableSignals "error"
	  BlockIODiagnostic	  "none"
	  SFUnusedDataAndEventsDiag "warning"
	  SFUnexpectedBacktrackingDiag "warning"
	  SFInvalidInputDataAccessInChartInitDiag "warning"
	  SFNoUnconditionalDefaultTransitionDiag "warning"
	  SFTransitionOutsideNaturalParentDiag "warning"
	}
	Simulink.HardwareCC {
	  $ObjectID		  6
	  Version		  "1.10.0"
	  ProdBitPerChar	  8
	  ProdBitPerShort	  16
	  ProdBitPerInt		  32
	  ProdBitPerLong	  32
	  ProdBitPerFloat	  32
	  ProdBitPerDouble	  64
	  ProdBitPerPointer	  32
	  ProdLargestAtomicInteger "Char"
	  ProdLargestAtomicFloat  "None"
	  ProdIntDivRoundTo	  "Undefined"
	  ProdEndianess		  "Unspecified"
	  ProdWordSize		  32
	  ProdShiftRightIntArith  on
	  ProdHWDeviceType	  "32-bit Generic"
	  TargetBitPerChar	  8
	  TargetBitPerShort	  16
	  TargetBitPerInt	  32
	  TargetBitPerLong	  32
	  TargetBitPerFloat	  32
	  TargetBitPerDouble	  64
	  TargetBitPerPointer	  32
	  TargetLargestAtomicInteger "Char"
	  TargetLargestAtomicFloat "None"
	  TargetShiftRightIntArith on
	  TargetIntDivRoundTo	  "Undefined"
	  TargetEndianess	  "Unspecified"
	  TargetWordSize	  32
	  TargetTypeEmulationWarnSuppressLevel 0
	  TargetPreprocMaxBitsSint 32
	  TargetPreprocMaxBitsUint 32
	  TargetHWDeviceType	  "Specified"
	  TargetUnknown		  off
	  ProdEqTarget		  on
	}
	Simulink.ModelReferenceCC {
	  $ObjectID		  7
	  Version		  "1.10.0"
	  UpdateModelReferenceTargets "IfOutOfDateOrStructuralChange"
	  CheckModelReferenceTargetMessage "error"
	  EnableParallelModelReferenceBuilds off
	  ParallelModelReferenceErrorOnInvalidPool on
	  ParallelModelReferenceMATLABWorkerInit "None"
	  ModelReferenceNumInstancesAllowed "Multi"
	  PropagateVarSize	  "Infer from blocks in model"
	  ModelReferencePassRootInputsByReference on
	  ModelReferenceMinAlgLoopOccurrences off
	  PropagateSignalLabelsOutOfModel off
	  SupportModelReferenceSimTargetCustomCode off
	}
	Simulink.SFSimCC {
	  $ObjectID		  8
	  Version		  "1.10.0"
	  SFSimEnableDebug	  on
	  SFSimOverflowDetection  on
	  SFSimEcho		  on
	  SimBlas		  on
	  SimCtrlC		  on
	  SimExtrinsic		  on
	  SimIntegrity		  on
	  SimUseLocalCustomCode	  off
	  SimParseCustomCode	  on
	  SimBuildMode		  "sf_incremental_build"
	}
	Simulink.RTWCC {
	  $BackupClass		  "Simulink.RTWCC"
	  $ObjectID		  9
	  Version		  "1.10.0"
	  Array {
	    Type		    "Cell"
	    Dimension		    1
	    Cell		    "IncludeHyperlinkInReport"
	    PropName		    "DisabledProps"
	  }
	  SystemTargetFile	  "grt.tlc"
	  GenCodeOnly		  off
	  MakeCommand		  "make_rtw"
	  GenerateMakefile	  on
	  TemplateMakefile	  "grt_default_tmf"
	  GenerateReport	  off
	  SaveLog		  off
	  RTWVerbose		  on
	  RetainRTWFile		  off
	  ProfileTLC		  off
	  TLCDebug		  off
	  TLCCoverage		  off
	  TLCAssert		  off
	  ProcessScriptMode	  "Default"
	  ConfigurationMode	  "Optimized"
	  ConfigAtBuild		  off
	  RTWUseLocalCustomCode	  off
	  RTWUseSimCustomCode	  off
	  IncludeHyperlinkInReport off
	  LaunchReport		  off
	  TargetLang		  "C"
	  IncludeBusHierarchyInRTWFileBlockHierarchyMap	off
	  IncludeERTFirstTime	  off
	  GenerateTraceInfo	  off
	  GenerateTraceReport	  off
	  GenerateTraceReportSl	  off
	  GenerateTraceReportSf	  off
	  GenerateTraceReportEml  off
	  GenerateCodeInfo	  off
	  GenerateSLWebview	  off
	  RTWCompilerOptimization "Off"
	  CheckMdlBeforeBuild	  "Off"
	  CustomRebuildMode	  "OnUpdate"
	  Array {
	    Type		    "Handle"
	    Dimension		    2
	    Simulink.CodeAppCC {
	      $ObjectID		      10
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		16
		Cell			"IgnoreCustomStorageClasses"
		Cell			"InsertBlockDesc"
		Cell			"SFDataObjDesc"
		Cell			"SimulinkDataObjDesc"
		Cell			"DefineNamingRule"
		Cell			"SignalNamingRule"
		Cell			"ParamNamingRule"
		Cell			"InlinedPrmAccess"
		Cell			"CustomSymbolStr"
		Cell			"CustomSymbolStrGlobalVar"
		Cell			"CustomSymbolStrType"
		Cell			"CustomSymbolStrField"
		Cell			"CustomSymbolStrFcn"
		Cell			"CustomSymbolStrBlkIO"
		Cell			"CustomSymbolStrTmpVar"
		Cell			"CustomSymbolStrMacro"
		PropName		"DisabledProps"
	      }
	      ForceParamTrailComments off
	      GenerateComments	      on
	      IgnoreCustomStorageClasses on
	      IgnoreTestpoints	      off
	      IncHierarchyInIds	      off
	      MaxIdLength	      31
	      PreserveName	      off
	      PreserveNameWithParent  off
	      ShowEliminatedStatement off
	      IncAutoGenComments      off
	      SimulinkDataObjDesc     off
	      SFDataObjDesc	      off
	      MATLABFcnDesc	      off
	      IncDataTypeInIds	      off
	      MangleLength	      1
	      CustomSymbolStrGlobalVar "$R$N$M"
	      CustomSymbolStrType     "$N$R$M"
	      CustomSymbolStrField    "$N$M"
	      CustomSymbolStrFcn      "$R$N$M$F"
	      CustomSymbolStrFcnArg   "rt$I$N$M"
	      CustomSymbolStrBlkIO    "rtb_$N$M"
	      CustomSymbolStrTmpVar   "$N$M"
	      CustomSymbolStrMacro    "$R$N$M"
	      DefineNamingRule	      "None"
	      ParamNamingRule	      "None"
	      SignalNamingRule	      "None"
	      InsertBlockDesc	      off
	      InsertPolySpaceComments off
	      SimulinkBlockComments   on
	      MATLABSourceComments    off
	      EnableCustomComments    off
	      InlinedPrmAccess	      "Literals"
	      ReqsInCode	      off
	      UseSimReservedNames     off
	    }
	    Simulink.GRTTargetCC {
	      $BackupClass	      "Simulink.TargetCC"
	      $ObjectID		      11
	      Version		      "1.10.0"
	      Array {
		Type			"Cell"
		Dimension		12
		Cell			"IncludeMdlTerminateFcn"
		Cell			"CombineOutputUpdateFcns"
		Cell			"SuppressErrorStatus"
		Cell			"ERTCustomFileBanners"
		Cell			"GenerateSampleERTMain"
		Cell			"GenerateTestInterfaces"
		Cell			"MultiInstanceERTCode"
		Cell			"PurelyIntegerCode"
		Cell			"SupportNonInlinedSFcns"
		Cell			"SupportComplex"
		Cell			"SupportAbsoluteTime"
		Cell			"SupportContinuousTime"
		PropName		"DisabledProps"
	      }
	      TargetFcnLib	      "ansi_tfl_tmw.mat"
	      TargetLibSuffix	      ""
	      TargetPreCompLibLocation ""
	      TargetFunctionLibrary   "ANSI_C"
	      UtilityFuncGeneration   "Auto"
	      ERTMultiwordTypeDef     "System defined"
	      ERTCodeCoverageTool     "None"
	      ERTMultiwordLength      256
	      MultiwordLength	      2048
	      GenerateFullHeader      on
	      GenerateSampleERTMain   off
	      GenerateTestInterfaces  off
	      IsPILTarget	      off
	      ModelReferenceCompliant on
	      ParMdlRefBuildCompliant on
	      CompOptLevelCompliant   on
	      IncludeMdlTerminateFcn  on
	      GeneratePreprocessorConditionals "Disable all"
	      CombineOutputUpdateFcns off
	      CombineSignalStateStructs	off
	      SuppressErrorStatus     off
	      ERTFirstTimeCompliant   off
	      IncludeFileDelimiter    "Auto"
	      ERTCustomFileBanners    off
	      SupportAbsoluteTime     on
	      LogVarNameModifier      "rt_"
	      MatFileLogging	      on
	      MultiInstanceERTCode    off
	      SupportNonFinite	      on
	      SupportComplex	      on
	      PurelyIntegerCode	      off
	      SupportContinuousTime   on
	      SupportNonInlinedSFcns  on
	      SupportVariableSizeSignals off
	      EnableShiftOperators    on
	      ParenthesesLevel	      "Nominal"
	      PortableWordSizes	      off
	      ModelStepFunctionPrototypeControlCompliant off
	      CPPClassGenCompliant    off
	      AutosarCompliant	      off
	      UseMalloc		      off
	      ExtMode		      off
	      ExtModeStaticAlloc      off
	      ExtModeTesting	      off
	      ExtModeStaticAllocSize  1000000
	      ExtModeTransport	      0
	      ExtModeMexFile	      "ext_comm"
	      ExtModeIntrfLevel	      "Level1"
	      RTWCAPISignals	      off
	      RTWCAPIParams	      off
	      RTWCAPIStates	      off
	      GenerateASAP2	      off
	    }
	    PropName		    "Components"
	  }
	}
	PropName		"Components"
      }
      Name		      "Configuration"
      CurrentDlgPage	      "Optimization"
      ConfigPrmDlgPosition    " [ 200, 197, 1080, 827 ] "
    }
    PropName		    "ConfigurationSets"
  }
  Simulink.ConfigSet {
    $PropName		    "ActiveConfigurationSet"
    $ObjectID		    1
  }
  BlockDefaults {
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    NamePlacement	    "normal"
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    ShowName		    on
    BlockRotation	    0
    BlockMirror		    off
  }
  AnnotationDefaults {
    HorizontalAlignment	    "center"
    VerticalAlignment	    "middle"
    ForegroundColor	    "black"
    BackgroundColor	    "white"
    DropShadow		    off
    FontName		    "Courier New"
    FontSize		    10
    FontWeight		    "normal"
    FontAngle		    "normal"
    UseDisplayTextAsClickCallback off
  }
  LineDefaults {
    FontName		    "Arial Black"
    FontSize		    12
    FontWeight		    "normal"
    FontAngle		    "normal"
  }
  BlockParameterDefaults {
    Block {
      BlockType		      CombinatorialLogic
      TruthTable	      "[0 0;0 1;0 1;1 0;0 1;1 0;1 0;1 1]"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      ComplexToRealImag
      Output		      "Real and imag"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Constant
      Value		      "1"
      VectorParams1D	      on
      SamplingMode	      "Sample based"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit from 'Constant value'"
      LockScale		      off
      SampleTime	      "inf"
      FramePeriod	      "inf"
      PreserveConstantTs      off
    }
    Block {
      BlockType		      Gain
      Gain		      "1"
      Multiplication	      "Element-wise(K.*u)"
      ParamMin		      "[]"
      ParamMax		      "[]"
      ParamDataTypeStr	      "Inherit: Same as input"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Memory
      X0		      "0"
      InheritSampleTime	      off
      LinearizeMemory	      off
      LinearizeAsDelay	      off
      StateMustResolveToSignalObject off
      RTWStateStorageClass    "Auto"
    }
    Block {
      BlockType		      Mux
      Inputs		      "4"
      DisplayOption	      "none"
      UseBusObject	      off
      BusObject		      "BusObject"
      NonVirtualBus	      off
    }
    Block {
      BlockType		      Product
      Inputs		      "2"
      Multiplication	      "Element-wise(.*)"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Zero"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      RealImagToComplex
      Input		      "Real and imag"
      ConstantPart	      "0"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Relay
      OnSwitchValue	      "eps"
      OffSwitchValue	      "eps"
      OnOutputValue	      "1"
      OffOutputValue	      "0"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: All ports same datatype"
      LockScale		      off
      ZeroCross		      on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Scope
      ModelBased	      off
      TickLabels	      "OneTimeTick"
      ZoomMode		      "on"
      Grid		      "on"
      TimeRange		      "auto"
      YMin		      "-5"
      YMax		      "5"
      SaveToWorkspace	      off
      SaveName		      "ScopeData"
      LimitDataPoints	      on
      MaxDataPoints	      "5000"
      Decimation	      "1"
      SampleInput	      off
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Sin
      SineType		      "Time based"
      TimeSource	      "Use simulation time"
      Amplitude		      "1"
      Bias		      "0"
      Frequency		      "1"
      Phase		      "0"
      Samples		      "10"
      Offset		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
    Block {
      BlockType		      Sum
      IconShape		      "rectangular"
      Inputs		      "++"
      CollapseMode	      "All dimensions"
      CollapseDim	      "1"
      InputSameDT	      on
      AccumDataTypeStr	      "Inherit: Inherit via internal rule"
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Same as first input"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      SampleTime	      "-1"
    }
    Block {
      BlockType		      Switch
      Criteria		      "u2 >= Threshold"
      Threshold		      "0"
      InputSameDT	      on
      OutMin		      "[]"
      OutMax		      "[]"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      LockScale		      off
      RndMeth		      "Floor"
      SaturateOnIntegerOverflow	on
      ZeroCross		      on
      SampleTime	      "-1"
      AllowDiffInputSizes     off
    }
    Block {
      BlockType		      Trigonometry
      Operator		      "sin"
      ApproximationMethod     "None"
      NumberOfIterations      "11"
      OutputSignalType	      "auto"
      SampleTime	      "-1"
    }
    Block {
      BlockType		      UniformRandomNumber
      Minimum		      "-1"
      Maximum		      "1"
      Seed		      "0"
      SampleTime	      "-1"
      VectorParams1D	      on
    }
  }
  System {
    Name		    "BPSK6"
    Location		    [228, 137, 1210, 832]
    Open		    on
    ModelBrowserVisibility  off
    ModelBrowserWidth	    200
    ScreenColor		    "white"
    PaperOrientation	    "rotated"
    PaperPositionMode	    "auto"
    PaperType		    "usletter"
    PaperUnits		    "inches"
    TiledPaperMargins	    [0.500000, 0.500000, 0.500000, 0.500000]
    TiledPageScale	    1
    ShowPageBoundaries	    off
    ZoomFactor		    "100"
    ReportName		    "simulink-default.rpt"
    SIDHighWatermark	    "55"
    Block {
      BlockType		      Constant
      Name		      "0"
      SID		      "1"
      Position		      [270, 475, 300, 505]
      Value		      "0"
    }
    Block {
      BlockType		      Constant
      Name		      "15"
      SID		      "2"
      Position		      [360, 590, 390, 620]
      Value		      "15"
    }
    Block {
      BlockType		      Sum
      Name		      "AddI"
      SID		      "3"
      Ports		      [2, 1]
      Position		      [850, 157, 880, 188]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"I'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sum
      Name		      "AddQ"
      SID		      "4"
      Ports		      [2, 1]
      Position		      [855, 307, 885, 338]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Q'"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      ComplexToRealImag
      Name		      "C->Re,Im"
      SID		      "47"
      Ports		      [1, 2]
      Position		      [550, 133, 580, 162]
      Output		      "Real and imag"
      Port {
	PortNumber		1
	Name			"I"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
      Port {
	PortNumber		2
	Name			"Q"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "Carrier"
      SID		      "5"
      Ports		      [0, 1]
      Position		      [100, 85, 130, 115]
      Frequency		      "2513274.1229"
      Phase		      "pi/2"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"c(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "Carrier1"
      SID		      "50"
      Ports		      [0, 1]
      Position		      [100, 135, 130, 165]
      Frequency		      "2513274.1229"
      SampleTime	      "7.8125e-008"
      Port {
	PortNumber		1
	Name			"s(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "CosGen"
      SID		      "6"
      Ports		      [1, 1]
      Position		      [550, 545, 580, 575]
      Operator		      "cos"
      Port {
	PortNumber		1
	Name			"cos"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Constant
      Name		      "CounterCLK"
      SID		      "7"
      Position		      [95, 535, 125, 565]
      SampleTime	      "6.25e-007"
    }
    Block {
      BlockType		      Memory
      Name		      "Delay"
      SID		      "53"
      Position		      [320, 640, 350, 670]
      BlockMirror	      on
    }
    Block {
      BlockType		      Memory
      Name		      "DelayI"
      SID		      "52"
      Position		      [105, 35, 135, 65]
      BlockMirror	      on
    }
    Block {
      BlockType		      Memory
      Name		      "DelayQ"
      SID		      "51"
      Position		      [95, 440, 125, 470]
      BlockMirror	      on
    }
    Block {
      BlockType		      CombinatorialLogic
      Name		      "EXOR"
      SID		      "11"
      Position		      [150, 335, 180, 365]
      TruthTable	      "[0;1;1;0]"
      Port {
	PortNumber		1
	Name			"I'_del xor Q'_del"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Gain
      Name		      "Gain"
      SID		      "14"
      Position		      [610, 620, 640, 650]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Scope
      Name		      "I Q"
      SID		      "48"
      Ports		      [2]
      Position		      [630, 186, 660, 219]
      Floating		      off
      Location		      [74, 594, 568, 833]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData2"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "50000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "I'Q'"
      SID		      "15"
      Ports		      [2]
      Position		      [945, 216, 975, 249]
      Floating		      off
      Location		      [79, 340, 571, 579]
      Open		      on
      NumInputPorts	      "2"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1"
      YMax		      "1.1~1.1"
      SaveName		      "ScopeData1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "50000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Scope
      Name		      "IbQb"
      SID		      "17"
      Ports		      [4]
      Position		      [225, 282, 255, 343]
      Floating		      off
      Location		      [427, 562, 910, 901]
      Open		      on
      NumInputPorts	      "4"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
	axes4			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1~-0.1~0"
      YMax		      "1.1~1.1~1.1~15"
      SaveName		      "ScopeData6"
      DataFormat	      "StructureWithTime"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Gain
      Name		      "Inverter"
      SID		      "18"
      Position		      [165, 575, 195, 605]
      Gain		      "-1"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI1"
      SID		      "22"
      Ports		      [2, 1]
      Position		      [785, 132, 815, 163]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulI2"
      SID		      "23"
      Ports		      [2, 1]
      Position		      [785, 192, 815, 223]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ1"
      SID		      "24"
      Ports		      [2, 1]
      Position		      [785, 262, 815, 293]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "MulQ2"
      SID		      "25"
      Ports		      [2, 1]
      Position		      [785, 337, 815, 368]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Mux
      Name		      "Mux"
      SID		      "26"
      Ports		      [2, 1]
      Position		      [125, 331, 130, 369]
      ShowName		      off
      Inputs		      "2"
      DisplayOption	      "bar"
    }
    Block {
      BlockType		      Product
      Name		      "Product"
      SID		      "46"
      Ports		      [2, 1]
      Position		      [495, 132, 525, 163]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Product
      Name		      "Product1"
      SID		      "49"
      Ports		      [2, 1]
      Position		      [370, 107, 400, 138]
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"s+(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Scope
      Name		      "RF Signal"
      SID		      "27"
      Ports		      [3]
      Position		      [280, 248, 310, 282]
      Floating		      off
      Location		      [78, 66, 569, 333]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      YMin		      "-1.1~-1.1~-1.1"
      YMax		      "1.1~1.1~1.1"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "10000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      UniformRandomNumber
      Name		      "Random"
      SID		      "28"
      Position		      [15, 194, 45, 226]
      SampleTime	      "1e-005"
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "Re,Im->C"
      SID		      "44"
      Ports		      [2, 1]
      Position		      [280, 86, 320, 144]
      Input		      "Real and imag"
    }
    Block {
      BlockType		      RealImagToComplex
      Name		      "Re,Im->C2"
      SID		      "45"
      Ports		      [2, 1]
      Position		      [415, 231, 455, 289]
      Input		      "Real and imag"
    }
    Block {
      BlockType		      Relay
      Name		      "Relay"
      SID		      "29"
      Position		      [100, 195, 130, 225]
      OffSwitchValue	      "-eps"
      OffOutputValue	      "-1"
      Port {
	PortNumber		1
	Name			"m(t)"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "Relay1"
      SID		      "30"
      Position		      [65, 310, 95, 340]
      Port {
	PortNumber		1
	Name			"I'_del"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Relay
      Name		      "Relay2"
      SID		      "31"
      Position		      [65, 375, 95, 405]
      Port {
	PortNumber		1
	Name			"Q'_del"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Select\nUp/Down"
      SID		      "32"
      Position		      [225, 545, 255, 575]
      Threshold		      "0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"Counter\nInput"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Trigonometry
      Name		      "SinGen"
      SID		      "33"
      Ports		      [1, 1]
      Position		      [550, 620, 580, 650]
    }
    Block {
      BlockType		      Sum
      Name		      "Sum"
      SID		      "34"
      Ports		      [2, 1]
      Position		      [285, 550, 305, 570]
      ShowName		      off
      IconShape		      "round"
      Inputs		      "|++"
      InputSameDT	      off
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"ACC\nout"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap<0"
      SID		      "37"
      Position		      [425, 545, 455, 575]
      Threshold		      "-0.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"C_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Switch
      Name		      "Wrap>15"
      SID		      "38"
      Position		      [355, 535, 385, 565]
      Threshold		      "15.5"
      InputSameDT	      off
      SaturateOnIntegerOverflow	off
    }
    Block {
      BlockType		      Sin
      Name		      "cos"
      SID		      "39"
      Ports		      [0, 1]
      Position		      [355, 220, 385, 250]
      Frequency		      "2450442.2698"
      Phase		      "pi/2"
      SampleTime	      "8.0128e-008"
    }
    Block {
      BlockType		      Scope
      Name		      "cos/sin"
      SID		      "40"
      Ports		      [3]
      Position		      [795, 547, 830, 613]
      Floating		      off
      Location		      [70, 726, 558, 993]
      Open		      on
      NumInputPorts	      "3"
      ZoomMode		      "xonly"
      List {
	ListType		AxesTitles
	axes1			"%<SignalLabel>"
	axes2			"%<SignalLabel>"
	axes3			"%<SignalLabel>"
      }
      TimeRange		      "0.0002"
      YMin		      "-1.1~-1.1~-1"
      YMax		      "1.1~1.1~16"
      SaveName		      "ScopeData5"
      DataFormat	      "StructureWithTime"
      MaxDataPoints	      "50000"
      SampleTime	      "0"
    }
    Block {
      BlockType		      Gain
      Name		      "pi/8"
      SID		      "41"
      Position		      [490, 545, 520, 575]
      Gain		      "pi/8"
      ParamDataTypeStr	      "Inherit: Inherit via internal rule"
      OutDataTypeStr	      "Inherit: Inherit via internal rule"
      SaturateOnIntegerOverflow	off
      Port {
	PortNumber		1
	Name			"phi_out"
	RTWStorageClass		"Auto"
	DataLoggingNameMode	"SignalName"
      }
    }
    Block {
      BlockType		      Sin
      Name		      "sin"
      SID		      "42"
      Ports		      [0, 1]
      Position		      [355, 270, 385, 300]
      Amplitude		      "-1"
      Frequency		      "2450442.2698"
      SampleTime	      "8.0128e-008"
    }
    Line {
      Name		      "m(t)"
      Labels		      [0, 0; 1, 0]
      SrcBlock		      "Relay"
      SrcPort		      1
      Points		      [90, 0]
      Branch {
	Points			[0, 55]
	DstBlock		"RF Signal"
	DstPort			2
      }
      Branch {
	Points			[115, 0; 0, -80]
	DstBlock		"Product1"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "Random"
      SrcPort		      1
      DstBlock		      "Relay"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Delay"
      SrcPort		      1
      Points		      [-20, 0]
      DstBlock		      "Sum"
      DstPort		      2
    }
    Line {
      Name		      "ACC\nout"
      Labels		      [0, 0]
      SrcBlock		      "Sum"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Labels			[1, 0]
	DstBlock		"Wrap>15"
	DstPort			3
      }
      Branch {
	Points			[0, -10]
	DstBlock		"Wrap>15"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "0"
      SrcPort		      1
      Points		      [15, 0; 0, 50]
      DstBlock		      "Wrap>15"
      DstPort		      1
    }
    Line {
      SrcBlock		      "Wrap>15"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Wrap<0"
	DstPort			1
      }
      Branch {
	Points			[0, 10]
	DstBlock		"Wrap<0"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "15"
      SrcPort		      1
      Points		      [0, -35]
      DstBlock		      "Wrap<0"
      DstPort		      3
    }
    Line {
      Name		      "C_out"
      Labels		      [0, 0]
      SrcBlock		      "Wrap<0"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	Points			[0, -40; -255, 0]
	DstBlock		"IbQb"
	DstPort			4
      }
      Branch {
	DstBlock		"pi/8"
	DstPort			1
      }
      Branch {
	Points			[0, 40]
	Branch {
	  DstBlock		  "cos/sin"
	  DstPort		  3
	}
	Branch {
	  Points		  [0, 55]
	  DstBlock		  "Delay"
	  DstPort		  1
	}
      }
    }
    Line {
      SrcBlock		      "Inverter"
      SrcPort		      1
      Points		      [10, 0]
      DstBlock		      "Select\nUp/Down"
      DstPort		      3
    }
    Line {
      Name		      "Counter\nInput"
      Labels		      [0, 0]
      SrcBlock		      "Select\nUp/Down"
      SrcPort		      1
      DstBlock		      "Sum"
      DstPort		      1
    }
    Line {
      SrcBlock		      "CounterCLK"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, 40]
	DstBlock		"Inverter"
	DstPort			1
      }
      Branch {
	DstBlock		"Select\nUp/Down"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "SinGen"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"Gain"
	DstPort			1
      }
      Branch {
	Points			[0, -55]
	Branch {
	  DstBlock		  "cos/sin"
	  DstPort		  2
	}
	Branch {
	  Labels		  [2, 0]
	  Points		  [0, -295]
	  DstBlock		  "MulQ1"
	  DstPort		  2
	}
      }
    }
    Line {
      Name		      "cos"
      Labels		      [0, 0]
      SrcBlock		      "CosGen"
      SrcPort		      1
      Points		      [105, 0]
      Branch {
	Points			[0, -195]
	Branch {
	  Points		  [80, 0]
	  DstBlock		  "MulQ2"
	  DstPort		  2
	}
	Branch {
	  Points		  [0, -210]
	  DstBlock		  "MulI1"
	  DstPort		  2
	}
      }
      Branch {
	Labels			[1, 0]
	DstBlock		"cos/sin"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Gain"
      SrcPort		      1
      Points		      [125, 0]
      DstBlock		      "MulI2"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulI1"
      SrcPort		      1
      Points		      [5, 0; 0, 15]
      DstBlock		      "AddI"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulI2"
      SrcPort		      1
      Points		      [5, 0; 0, -30]
      DstBlock		      "AddI"
      DstPort		      2
    }
    Line {
      SrcBlock		      "MulQ1"
      SrcPort		      1
      Points		      [10, 0; 0, 35]
      DstBlock		      "AddQ"
      DstPort		      1
    }
    Line {
      SrcBlock		      "MulQ2"
      SrcPort		      1
      Points		      [10, 0; 0, -25]
      DstBlock		      "AddQ"
      DstPort		      2
    }
    Line {
      Name		      "I'_del"
      Labels		      [0, 0]
      SrcBlock		      "Relay1"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	Points			[0, 15]
	DstBlock		"Mux"
	DstPort			1
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -35]
	DstBlock		"IbQb"
	DstPort			1
      }
    }
    Line {
      Name		      "Q'_del"
      Labels		      [0, 0]
      SrcBlock		      "Relay2"
      SrcPort		      1
      Points		      [5, 0; 0, -30; 5, 0]
      Branch {
	DstBlock		"Mux"
	DstPort			2
      }
      Branch {
	Labels			[2, 0]
	Points			[0, -55]
	DstBlock		"IbQb"
	DstPort			2
      }
    }
    Line {
      SrcBlock		      "Mux"
      SrcPort		      1
      DstBlock		      "EXOR"
      DstPort		      1
    }
    Line {
      Name		      "I'_del xor Q'_del"
      Labels		      [0, 0]
      SrcBlock		      "EXOR"
      SrcPort		      1
      Points		      [10, 0]
      Branch {
	Points			[0, 210]
	DstBlock		"Select\nUp/Down"
	DstPort			2
      }
      Branch {
	Points			[0, -30]
	DstBlock		"IbQb"
	DstPort			3
      }
    }
    Line {
      Name		      "I'"
      Labels		      [0, 0]
      SrcBlock		      "AddI"
      SrcPort		      1
      Points		      [25, 0]
      Branch {
	Points			[0, 50]
	DstBlock		"I'Q'"
	DstPort			1
      }
      Branch {
	Points			[0, -125]
	DstBlock		"DelayI"
	DstPort			1
      }
    }
    Line {
      Name		      "Q'"
      Labels		      [0, 0]
      SrcBlock		      "AddQ"
      SrcPort		      1
      Points		      [20, 0]
      Branch {
	Points			[0, -85]
	DstBlock		"I'Q'"
	DstPort			2
      }
      Branch {
	Points			[0, 130]
	DstBlock		"DelayQ"
	DstPort			1
      }
    }
    Line {
      Name		      "phi_out"
      Labels		      [0, 0]
      SrcBlock		      "pi/8"
      SrcPort		      1
      Points		      [5, 0]
      Branch {
	DstBlock		"CosGen"
	DstPort			1
      }
      Branch {
	Points			[0, 75]
	DstBlock		"SinGen"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "cos"
      SrcPort		      1
      Points		      [5, 0; 0, 10]
      DstBlock		      "Re,Im->C2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "sin"
      SrcPort		      1
      Points		      [5, 0; 0, -10]
      DstBlock		      "Re,Im->C2"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Re,Im->C2"
      SrcPort		      1
      Points		      [10, 0; 0, -105]
      DstBlock		      "Product"
      DstPort		      2
    }
    Line {
      SrcBlock		      "Product"
      SrcPort		      1
      DstBlock		      "C->Re,Im"
      DstPort		      1
    }
    Line {
      Name		      "I"
      SrcBlock		      "C->Re,Im"
      SrcPort		      1
      Points		      [30, 0]
      Branch {
	DstBlock		"I Q"
	DstPort			1
      }
      Branch {
	Labels			[1, 0]
	Points			[60, 0]
	Branch {
	  DstBlock		  "MulI1"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, 130]
	  DstBlock		  "MulQ1"
	  DstPort		  1
	}
      }
    }
    Line {
      Name		      "Q"
      SrcBlock		      "C->Re,Im"
      SrcPort		      2
      Points		      [15, 0; 0, 55]
      Branch {
	DstBlock		"I Q"
	DstPort			2
      }
      Branch {
	Labels			[2, 0]
	Points			[0, 135; 140, 0]
	Branch {
	  DstBlock		  "MulQ2"
	  DstPort		  1
	}
	Branch {
	  Points		  [0, -145]
	  DstBlock		  "MulI2"
	  DstPort		  1
	}
      }
    }
    Line {
      Name		      "c(t)"
      Labels		      [0, 0; 0, 0; 1, 0]
      SrcBlock		      "Carrier"
      SrcPort		      1
      Points		      [105, 0]
      Branch {
	DstBlock		"Re,Im->C"
	DstPort			1
      }
      Branch {
	Points			[0, 155]
	DstBlock		"RF Signal"
	DstPort			1
      }
    }
    Line {
      SrcBlock		      "Re,Im->C"
      SrcPort		      1
      DstBlock		      "Product1"
      DstPort		      1
    }
    Line {
      Name		      "s+(t)"
      Labels		      [0, 0]
      SrcBlock		      "Product1"
      SrcPort		      1
      Points		      [35, 0; 0, 15]
      DstBlock		      "Product"
      DstPort		      1
    }
    Line {
      Name		      "s(t)"
      Labels		      [0, 0]
      SrcBlock		      "Carrier1"
      SrcPort		      1
      Points		      [120, 0]
      Branch {
	Points			[0, -20]
	DstBlock		"Re,Im->C"
	DstPort			2
      }
      Branch {
	Points			[0, 125]
	DstBlock		"RF Signal"
	DstPort			3
      }
    }
    Line {
      SrcBlock		      "DelayQ"
      SrcPort		      1
      Points		      [-50, 0; 0, -65]
      DstBlock		      "Relay2"
      DstPort		      1
    }
    Line {
      SrcBlock		      "DelayI"
      SrcPort		      1
      Points		      [-50, 0]
      DstBlock		      "Relay1"
      DstPort		      1
    }
    Annotation {
      Name		      "LO"
      Position		      [466, 268]
    }
    Annotation {
      Position		      [371, 285]
    }
    Annotation {
      Name		      "s"
      Position		      [158, 149]
    }
  }
}
