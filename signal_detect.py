import numpy as np
import matplotlib.pyplot as plt
from matplotlib_config import configure
import utility

CHINESE_FONT_AVAILABLE = configure()

def rrc(t, rolloff_factor):
    h = np.zeros(len(t))
    for i in range(len(t)):
        if t[i] == 0:
            h[i] = 1 - rolloff_factor + (4 * rolloff_factor / np.pi)
        elif abs(t[i]) == 1 / (4 * rolloff_factor):
            h[i] = (rolloff_factor / np.sqrt(2)) * ((1 + 2 / np.pi) * np.sin(np.pi / (4 * rolloff_factor)) + (1 - 2 / np.pi) * np.cos(np.pi / (4 * rolloff_factor)))
        else:
            h[i] = (np.sin(np.pi * t[i] * (1 - rolloff_factor)) + 4 * rolloff_factor * t[i] * np.cos(np.pi * t[i] * (1 + rolloff_factor)))
            h[i] /= (np.pi * t[i] * (1 - (4 * rolloff_factor * t[i]) ** 2))
    return h


def generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor):
    simulation_time = num_symbols / symbol_rate
    t = np.arange(0, simulation_time, time_granularity)

    impulse_series = np.zeros(len(t))
    for i in range(num_symbols):
        impulse_series[round(i / symbol_rate / time_granularity)] = message[i]

    # RRC filter
    filter_length = round(filter_symbols / symbol_rate / time_granularity)
    if filter_length % 2 == 0:
        filter_length += 1
    t_filter = np.arange(-(filter_length - 1) / 2, (filter_length + 1) / 2) * time_granularity * symbol_rate
    rrc_response = rrc(t_filter, rolloff_factor)

    baseband_signal = np.convolve(impulse_series, rrc_response)
    t = np.arange(0, len(baseband_signal) * time_granularity, time_granularity)
    return t, baseband_signal


def sample_and_filter(signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor):
    sample_rate = symbol_rate * over_sampling_ratio
    num_samples = (num_symbols + filter_symbols - 1) * over_sampling_ratio
    sampled_signal = np.zeros(num_samples, dtype=complex)
    for i in range(num_samples):
        sampled_signal[i] = signal[round(((i / sample_rate) + (sample_offset / symbol_rate)) / time_granularity)]

    discrete_filter_length = round(filter_symbols * over_sampling_ratio)
    if discrete_filter_length % 2 == 0:
        discrete_filter_length += 1
    t_discrete_filter = np.arange(-(discrete_filter_length - 1) / 2, (discrete_filter_length + 1) / 2) / over_sampling_ratio
    discrete_rrc_response = rrc(t_discrete_filter, rolloff_factor) / over_sampling_ratio

    filtered_signal = np.convolve(sampled_signal, discrete_rrc_response)
    return filtered_signal


def detect_signal(signal, energy_threshold, consecutive_threshold):
    signal_energy = np.abs(signal) ** 2
    signal_detected = signal_energy > energy_threshold
    consecutive_count = 0
    for i in range(len(signal_detected)):
        if signal_detected[i]:
            consecutive_count += 1
            if consecutive_count >= consecutive_threshold:
                return i + 1
        else:
            consecutive_count = 0
    return -1


def signal_detect():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 60 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = [0] * 20 + [1] * 20 + [0] * 20
    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    freq_offset = 43600 # Hz
    phase_offset = 0.3 # radians
    noise_std = 1e-3

    received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
    received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
    received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

    over_sampling_ratio = 4 # sps, samples per symbol
    sample_offset = 0 # per symbol, from 0 to 1/sps

    sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)
    sampled_signal = utility.load_signal("gain20.npy")  # Load the sampled signal from a file

    window_length = round(2 * over_sampling_ratio)
    ratio12_list = []
    ratio21_list = []
    for i in range(len(sampled_signal) - 2 * window_length + 1):
        window1 = sampled_signal[i:i + window_length]
        window2 = sampled_signal[i + window_length:i + 2 * window_length]
        energy1 = np.sum(np.abs(window1) ** 2)
        energy2 = np.sum(np.abs(window2) ** 2)
        # zero_crossing_num2 = np.sum(np.abs(np.diff(np.sign(window2)))) / 2
        ratio12 = energy1 / energy2
        ratio21 = energy2 / energy1
        ratio12_list.append(ratio12)
        ratio21_list.append(ratio21)

    ratio_threshold = 25
    for i in range(len(ratio21_list)):
        if ratio21_list[i] > ratio_threshold:
            signal_start = i + window_length
            break
    print(f"Signal detected at sample index: {signal_start}")
    for i in range(len(ratio12_list)):
        if ratio12_list[i] > ratio_threshold:
            signal_end = i + window_length
            break
    print(f"Signal ends at sample index: {signal_end}")

    plt.figure(figsize=(10, 5))
    plt.plot(sampled_signal.real, label='Real Part')
    plt.plot(sampled_signal.imag, label='Imaginary Part')
    plt.title('Sampled Signal')
    plt.xlabel('Sample Index')
    plt.ylabel('Amplitude')
    plt.legend()
    plt.grid()
    plt.show()

    plt.figure(figsize=(10, 5))
    plt.plot(ratio12_list)
    plt.title('Energy Ratio 1/2')
    plt.xlabel('Sample Index')
    plt.ylabel('Ratio')
    plt.grid()
    plt.show()

    plt.figure(figsize=(10, 5))
    plt.plot(ratio21_list)
    plt.title('Energy Ratio 2/1')
    plt.xlabel('Sample Index')
    plt.ylabel('Ratio')
    plt.grid()
    plt.show()


if __name__ == "__main__":
    signal_detect()
